import request from '@/utils/request'


export function getRecentDevices() {
  return request({
    url: '/space/myRecentDevices',
    method: 'get'
  })
}

export function findDevice(data) {
  return request({
    url: '/space/findDevice',
    method: 'Post',
	data: data
  })
}

export function addDevice(data) {
  return request({
    url: '/space/addDevice',
    method: 'post',
	data: {
      data: data
    }
  })
}

export function getCollectDevices() {
  return request({
    url: '/space/getCollectDevices',
    method: 'Post'
  })
}

export function collectDevice(data) {
  return request({
    url: '/space/collectDevice',
    method: 'post',
	data: {
      data
    }
  })
}

export function pullDeviceInfo(data) {
  return request({
    url: '/device/'+data.deviceId+"/consumer/"+data.clientId+"_"+data.page,
	timeout: 20000,
    method: 'get'
  })
}

export function getDeviceInvoke(data) {
  return request({
    url: '/app/device/service/invoke',
    method: 'Post',
    data: {
      data
    }
  })
}

export function getDeviceDetail(data) {
  return request({
    url: '/app/device/detail',
    method: 'Post',
    data
  })
}

export function setProperty(data) {
  return request({
    url: '/app/device/service/property/set',
    method: 'Post',
    data
  })
}

export function removeDevice(data) {
  return request({
    url: '/space/removeDevice',
    method: 'Post',
    data
  })
}

export function invoke(data) {
  return request({
    url: '/app/device/service/invoke',
    method: 'Post',
    data
  })
}
