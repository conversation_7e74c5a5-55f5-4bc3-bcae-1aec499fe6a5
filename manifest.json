{"name": "CIMS智能运维平台", "appid": "__UNI__130B8F9", "description": "CIMS智能运维平台 - 物联网设备管理系统", "versionName": "1.1.0", "versionCode": "100", "transformPx": false, "app-plus": {"usingComponents": true, "nvueCompiler": "uni-app", "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.INTERNET\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>"], "abiFilters": ["armeabi-v7a", "arm64-v8a", "x86"]}, "ios": {"dSYMs": false, "privacyDescription": {"NSCameraUsageDescription": "此应用需要访问摄像头以进行设备监控", "NSLocationWhenInUseUsageDescription": "此应用需要访问位置信息以进行设备定位", "NSLocationAlwaysAndWhenInUseUsageDescription": "此应用需要访问位置信息以进行设备定位"}, "capabilities": {"entitlements": {"com.apple.developer.networking.wifi-info": true}}}, "sdkConfigs": {"ad": {}, "oauth": {}, "push": {}, "share": {}}}}, "quickapp": {}, "mp-weixin": {"appid": "wx44559984df3ab083", "setting": {"urlCheck": true, "es6": true, "minified": true, "postcss": false}, "optimization": {"subPackages": true}, "usingComponents": true, "permission": {}, "lazyCodeLoading": "requiredComponents"}, "vueVersion": "2", "h5": {"template": "static/index.html", "devServer": {"port": 9090, "https": false}, "title": "CIMS智能运维平台", "router": {"mode": "hash", "base": "./"}}, "locale": "zh-Hans", "fallbackLocale": "zh-Hans"}