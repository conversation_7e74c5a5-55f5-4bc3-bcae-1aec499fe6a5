<template>
  <view class="page-container">

    <u-loading-page-component :visible="isLoading" />


    <view class="fixed-header-title">
      <AppHeader flex title="[CIMS]指标看板" />
      <view class="header-decoration">
        <view class="deco-line left"></view>
        <view class="deco-line right"></view>
        <view class="header-circuit">
          <view class="circuit-dot"></view>
          <view class="circuit-line"></view>
        </view>
      </view>
    </view>


    <view class="fixed-tabs">
      <view class="industrial-tabs">
        <view class="tabs-frame">
          <view class="corner-tab tl"></view>
          <view class="corner-tab tr"></view>
          <view class="corner-tab bl"></view>
          <view class="corner-tab br"></view>
        </view>
        <view class="tabs-content">
          <view
            class="tab-item"
            v-for="tab in tabsList"
            :key="tab.id"
            :class="{ 'active': currentTabId === tab.id }"
            @click="clickTab(tab)"
          > 
            <view class="tab-indicator" v-if="currentTabId === tab.id">
              <view class="indicator-pulse"></view>
            </view>
            <text class="tab-text">{{ tab.name }}</text>
            <view class="tab-line" v-if="currentTabId === tab.id"></view>
          </view>


          <view class="refresh-btn" @click="refreshCurrentTab">
            <view class="refresh-icon" :class="{ 'rotating': isRefreshing }">
              <text class="refresh-text">⟳</text>
            </view>
          </view>
        </view>
      </view>
    </view>


    <view class="content-area">
      <scroll-view class="content-scroll" scroll-y="true">
        <view class="data-container" :class="{ 'data-loading': isTabSwitching }">
          <view
            class="industrial-card"
            v-for="(arr, index) in kanbandata"
            :key="index"
            @click="goEcharts(arr)"
          >

            <view class="card-frame">
              <view class="corner-card tl"></view>
              <view class="corner-card tr"></view>
              <view class="corner-card bl"></view>
              <view class="corner-card br"></view>
            </view>


            <view class="card-indicator">
              <view class="indicator-dot">
                <view class="dot-pulse"></view>
              </view>
              <view class="indicator-line"></view>
            </view>


            <view class="card-content">
              <view class="metric-item" v-for="(item, i) in arr" :key="i">
                <view class="metric-header">

                </view>
                <view class="metric-title">{{ item.name || "" }}</view>
                <view class="metric-value">
                  <text class="value-number">{{ item.value == null ? "__" : item.value }}</text>
                  <text class="value-unit">{{ item.unit || "" }}</text>
                </view>
                <view class="metric-line"></view>
              </view>
            </view>


            <view class="card-action">
              <view class="action-icon">→</view>
              <text class="action-text">点击查看详情</text>
            </view>
          </view>
        </view>


        <EmptyData
          :show="kanbandata.length === 0 && !isLoading"
          title="暂无指标数据"
          description="当前项目下没有配置相关指标信息"
        />
      </scroll-view>
    </view>
  </view>
</template>

<script>
import empyt from '@/components/controlPanel/controlPanel.vue';
import ULoadingPageComponent from '@/loading/index.vue';
import EmptyData from '@/components/EmptyData/EmptyData.vue';
import {
  powerPointConfiglable,
  powerPointConfigData
} from "@/api/kanban/kanban";
import { getProject } from "@/utils/auth.js";

export default {
  components: {
    empyt,
    ULoadingPageComponent,
    EmptyData
  },
  data() {
    return {
      iconColor: '#f5f5f5',
      iconShow: true,
      activeTab: 0,
      kanbandata: [],
      project: {},

      tabsList: [],
      isLoading: false,
      dataCache: {},
      currentTabId: null,
      isTabSwitching: false,
      isRefreshing: false
    };
  },
  onPullDownRefresh(){
	  this.clearCache();

	  if (this.currentTabId) {
		  this.getDataForIds(this.currentTabId, true).finally(() => {
			  setTimeout(() => {
				  uni.stopPullDownRefresh();
			  }, 300);
		  });
	  } else {
		  this.getData().finally(() => {
			  setTimeout(() => {
				  uni.stopPullDownRefresh();
			  }, 300);
		  });
	  }
  },
  methods: {
    clickTab(tab) {
      console.log('切换标签页:', tab);

      if (this.currentTabId === tab.id) {
        console.log('重新加载当前标签页数据');
      }

      this.currentTabId = tab.id;

      this.isTabSwitching = true;
      uni.showLoading({
        title: '加载中...',
        mask: true
      });

      this.getDataForIds(tab.id, true).finally(() => {
        uni.hideLoading();

        setTimeout(() => {
          this.isTabSwitching = false;
        }, 150);
      });
    },
    goEcharts(box) {
      const dataString = JSON.stringify(box);


      let encodedData;
      try {
        if (uni.base64 && uni.base64.encode) {
          encodedData = uni.base64.encode(dataString);
        } else {
          encodedData = btoa(encodeURIComponent(dataString));
        }
      } catch (error) {
        encodedData = encodeURIComponent(dataString);
      }

      uni.navigateTo({
        url: '/kanbanEcharts/echarts?data=' + encodedData
      });
    },
    async getData() {
      this.isLoading = true;
      try {
        this.project = getProject();
        const params = {
          data: {
            projectId: this.project.id,
            configType: 4
          }
        };
        const res = await powerPointConfiglable(params);
        if (res.data.length > 0) {
          this.tabsList = res.data.map(item => ({
            name: item.name,
            id: item.id
          }));
          

          this.currentTabId = this.tabsList[0].id;
          await this.getDataForIds(this.tabsList[0].id);
        } else {
          this.tabsList = [];
          this.kanbandata = [];
          this.currentTabId = null;
        }
      } catch (error) {
        console.error('错误响应:', error);
        this.tabsList = [];
        this.kanbandata = [];
      } finally {
        this.isLoading = false;
      }
    },
    async getDataForIds(id, showLoading = true) {
      if (showLoading) {
        this.isLoading = true;
      }

      try {
        const params = {
          data: id
        };

        const res = await powerPointConfigData(params);
        const data = res.data.datas.length > 0 ? res.data.datas : [];

        if (this.currentTabId === id) {
          this.kanbandata = data;
        }
        this.dataCache[id] = data;

      } catch (error) {

        uni.showToast({
          title: '数据加载失败',
          icon: 'none',
          duration: 2000
        });

        if (this.currentTabId === id) {
          this.kanbandata = [];
        }
        this.dataCache[id] = [];

      } finally {
        if (showLoading) {
          this.isLoading = false;
        }
      }
    },

    

    clearCache() {
      this.dataCache = {};
    },

    // 刷新当前标签页数据
    refreshCurrentTab() {
      if (!this.currentTabId) {
        uni.showToast({
          title: '请先选择标签页',
          icon: 'none'
        });
        return;
      }

      console.log('手动刷新当前标签页:', this.currentTabId);

      // 开始刷新动画
      this.isRefreshing = true;

      // 显示刷新提示
      uni.showToast({
        title: '刷新中...',
        icon: 'loading',
        duration: 1000
      });

      delete this.dataCache[this.currentTabId];

      this.getDataForIds(this.currentTabId, true).finally(() => {
        setTimeout(() => {
          this.isRefreshing = false;
        }, 500);
        uni.showToast({
          title: '刷新完成',
          icon: 'success',
          duration: 1000
        });
      });
    }
  },
  mounted() {

  },
  onShow() {
    this.getData();
  }
};
</script>

<style scoped>

.page-container {
  position: relative;
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 50%, #0f1419 100%);
  background-attachment: fixed;
}

.page-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 25% 25%, rgba(33, 148, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(0, 255, 136, 0.03) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}


.fixed-header-title {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 50%, #0f1419 100%);
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid rgba(33, 148, 255, 0.1);
}

.header-decoration {
  position: relative;
  height: 4rpx;
  margin: 0 40rpx;
  overflow: hidden;
}

.deco-line {
  position: absolute;
  height: 100%;
  background: linear-gradient(90deg, transparent 0%, #2194FF 50%, transparent 100%);
  animation: lineFlow 3s linear infinite;
}

.deco-line.left {
  width: 40%;
  left: 0;
}

.deco-line.right {
  width: 40%;
  right: 0;
  animation-delay: 1.5s;
}

.header-circuit {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.circuit-dot {
  width: 8rpx;
  height: 8rpx;
  background: #2194FF;
  border-radius: 50%;
  box-shadow: 0 0 8rpx rgba(33, 148, 255, 0.6);
  animation: circuitPulse 2s ease-in-out infinite;
}

.circuit-line {
  width: 20rpx;
  height: 2rpx;
  background: linear-gradient(90deg, #2194FF 0%, transparent 100%);
}


.fixed-tabs {
  position: fixed;
  top: 200rpx;
  left: 0;
  right: 0;
  z-index: 999;
  background: linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 50%, #0f1419 100%);
  margin: 0 20rpx;
  border-bottom: 1rpx solid rgba(33, 148, 255, 0.1);
  padding-bottom: 16rpx;
}

.industrial-tabs {
  position: relative;
  background: linear-gradient(135deg, #131b2e 0%, #1e2a3a 100%);
  border: 1rpx solid #2a3441;
  border-radius: 12rpx;
  backdrop-filter: blur(10rpx);
  padding: 16rpx;
}

.tabs-frame {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.corner-tab {
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  border: 2rpx solid #2194FF;
}

.corner-tab.tl {
  top: -1rpx;
  left: -1rpx;
  border-right: none;
  border-bottom: none;
  border-radius: 16rpx 0 0 0;
}

.corner-tab.tr {
  top: -1rpx;
  right: -1rpx;
  border-left: none;
  border-bottom: none;
  border-radius: 0 16rpx 0 0;
}

.corner-tab.bl {
  bottom: -1rpx;
  left: -1rpx;
  border-right: none;
  border-top: none;
  border-radius: 0 0 0 16rpx;
}

.corner-tab.br {
  bottom: -1rpx;
  right: -1rpx;
  border-left: none;
  border-top: none;
  border-radius: 0 0 16rpx 0;
}

.tabs-content {
  display: flex;
  justify-content: space-around;
  align-items: center;
  position: relative;
}

.tab-item {
  position: relative;
  padding: 12rpx 16rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 100rpx;
}

.tab-item.active {
  transform: scale(1.05);
}

.tab-indicator {
  position: absolute;
  top: -8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 12rpx;
  height: 12rpx;
}

.indicator-pulse {
  width: 100%;
  height: 100%;
  background: #2194FF;
  border-radius: 50%;
  animation: tabPulse 2s ease-in-out infinite;
  box-shadow: 0 0 12rpx rgba(33, 148, 255, 0.6);
}

.tab-text {
  font-size: 26rpx;
  font-weight: 600;
  color: #cccccc;
  transition: color 0.3s ease;
  margin-bottom: 8rpx;
}

.tab-item.active .tab-text {
  color: #ffffff;
  text-shadow: 0 0 8rpx rgba(33, 148, 255, 0.5);
}

.tab-line {
  position: absolute;
  bottom: 0;
  left: 20%;
  right: 20%;
  height: 2rpx;
  background: linear-gradient(90deg, transparent 0%, #2194FF 50%, transparent 100%);
  animation: lineGlow 2s ease-in-out infinite;
}

/* 刷新按钮样式 */
.refresh-btn {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(33, 148, 255, 0.1) 0%, rgba(33, 148, 255, 0.05) 100%);
  border: 1rpx solid rgba(33, 148, 255, 0.3);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  background: linear-gradient(135deg, rgba(33, 148, 255, 0.2) 0%, rgba(33, 148, 255, 0.1) 100%);
  border-color: rgba(33, 148, 255, 0.5);
  transform: translateY(-50%) scale(1.05);
}

.refresh-btn:active {
  transform: translateY(-50%) scale(0.95);
}

.refresh-icon {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.refresh-icon.rotating {
  animation: refreshRotate 1s linear infinite;
}

.refresh-text {
  font-size: 32rpx;
  color: #2194FF;
  font-weight: bold;
  text-shadow: 0 0 8rpx rgba(33, 148, 255, 0.3);
}

/* 内容区域 */
.content-area {
  position: absolute;
  top: 320rpx; /* 标题栏 + Tab导航栏的总高度 */
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}


.content-scroll {
  height: 100%;
  position: relative;
}

.data-container {
  padding: 0 20rpx 30rpx;
  transition: opacity 0.15s ease-in-out;
}

.data-container.data-loading {
  opacity: 0.7;
}


.industrial-card {
  position: relative;
  margin-bottom: 20rpx;
  background: rgba(18, 34, 46, 0.7);
  border: 1rpx solid rgba(33, 148, 255, 0.2);
  border-radius: 16rpx;
  padding: 20rpx;
  backdrop-filter: blur(15rpx);
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.industrial-card:hover {
  transform: translateY(-4rpx);
  border-color: rgba(33, 148, 255, 0.4);
  box-shadow: 0 12rpx 40rpx rgba(33, 148, 255, 0.15);
}

.card-frame {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.corner-card {
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  border: 2rpx solid #2194FF;
  animation: cornerGlow 3s ease-in-out infinite;
}

.corner-card.tl {
  top: 8rpx;
  left: 8rpx;
  border-right: none;
  border-bottom: none;
}

.corner-card.tr {
  top: 8rpx;
  right: 8rpx;
  border-left: none;
  border-bottom: none;
  animation-delay: 0.75s;
}

.corner-card.bl {
  bottom: 8rpx;
  left: 8rpx;
  border-right: none;
  border-top: none;
  animation-delay: 1.5s;
}

.corner-card.br {
  bottom: 8rpx;
  right: 8rpx;
  border-left: none;
  border-top: none;
  animation-delay: 2.25s;
}

.card-indicator {
  position: absolute;
  top: 12rpx;
  right: 15rpx;
  display: flex;
  align-items: center;
}

.indicator-dot {
  position: relative;
  width: 12rpx;
  height: 12rpx;
  margin-right: 6rpx;
}

.dot-pulse {
  width: 100%;
  height: 100%;
  background: #2194FF;
  border-radius: 50%;
  animation: dotPulse 2s ease-in-out infinite;
  box-shadow: 0 0 8rpx rgba(33, 148, 255, 0.6);
}

.indicator-line {
  width: 30rpx;
  height: 2rpx;
  background: linear-gradient(90deg, #2194FF 0%, transparent 100%);
  animation: lineFlow 2s linear infinite;
}

.card-content {
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: space-between;
  padding: 0 10rpx;
}

.metric-item {
  position: relative;
  flex: 1;
  padding: 12rpx 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.metric-header {
  display: flex;
  justify-content: center;
  margin-bottom: 8rpx;
}

.metric-icon {
  position: relative;
  width: 16rpx;
  height: 16rpx;
  background: #2194FF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-glow {
  width: 6rpx;
  height: 6rpx;
  background: #ffffff;
  border-radius: 50%;
  animation: iconGlow 2s ease-in-out infinite;
}

.metric-title {
  font-size: 22rpx;
  font-weight: 500;
  color: rgba(204, 204, 204, 0.9);
  letter-spacing: 0.5rpx;
  margin-bottom: 8rpx;
  word-break: break-all;
  line-height: 1.3;
}

.metric-value {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 8rpx;
}

.value-number {
  font-size: 30rpx;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 0 8rpx rgba(33, 148, 255, 0.3);
  margin-bottom: 2rpx;
}

.value-unit {
  font-size: 18rpx;
  font-weight: 400;
  color: rgba(204, 204, 204, 0.7);
}

.metric-line {
  width: 80%;
  height: 2rpx;
  background: linear-gradient(90deg, transparent 0%, rgba(33, 148, 255, 0.3) 20%, rgba(33, 148, 255, 0.6) 50%, rgba(33, 148, 255, 0.3) 80%, transparent 100%);
  animation: metricLine 3s ease-in-out infinite;
}

.card-action {
  position: absolute;
  bottom: 12rpx;
  right: 20rpx;
  display: flex;
  align-items: center;
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.industrial-card:hover .card-action {
  opacity: 1;
}

.action-icon {
  font-size: 22rpx;
  color: #2194FF;
  margin-right: 6rpx;
  animation: actionPulse 2s ease-in-out infinite;
}

.action-text {
  font-size: 18rpx;
  color: rgba(204, 204, 204, 0.8);
}


.industrial-empty {
  position: relative;
  margin: 60rpx 30rpx;
  padding: 60rpx;
  background: rgba(18, 34, 46, 0.7);
  border: 1rpx solid rgba(33, 148, 255, 0.2);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
}

.empty-frame {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.corner-empty {
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  border: 2rpx solid #2194FF;
}

.corner-empty.tl {
  top: 8rpx;
  left: 8rpx;
  border-right: none;
  border-bottom: none;
}

.corner-empty.tr {
  top: 8rpx;
  right: 8rpx;
  border-left: none;
  border-bottom: none;
}

.corner-empty.bl {
  bottom: 8rpx;
  left: 8rpx;
  border-right: none;
  border-top: none;
}

.corner-empty.br {
  bottom: 8rpx;
  right: 8rpx;
  border-left: none;
  border-top: none;
}


@keyframes lineFlow {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes circuitPulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes refreshRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes tabPulse {
  0%, 100% {
    opacity: 0.8;
    transform: translateX(-50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translateX(-50%) scale(1.2);
  }
}

@keyframes lineGlow {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
    box-shadow: 0 0 8rpx rgba(33, 148, 255, 0.4);
  }
}

@keyframes cornerGlow {
  0%, 100% {
    opacity: 0.6;
    box-shadow: 0 0 4rpx rgba(33, 148, 255, 0.3);
  }
  50% {
    opacity: 1;
    box-shadow: 0 0 8rpx rgba(33, 148, 255, 0.6);
  }
}

@keyframes dotPulse {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.3);
  }
}

@keyframes iconGlow {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes metricLine {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes actionPulse {
  0%, 100% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(4rpx);
  }
}
</style>
