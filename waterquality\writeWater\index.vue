<template>
	<view class="arr">
		<AppHeader flex title="[CIMS]水质报告填写" :iconShow="iconShow" :iconColor="iconColor" />
		<view>
			<!-- 报告日期 -->
			<view class="content-date">
				<u-row @click="toggleCalendar" customStyle="padding: 20rpx 10rpx;borderBottom:1px solid #262a35">
					<u-col span="4">
						<view class="data-text">报告日期</view>
					</u-col>
					<u-col span="6">
						<view class="data-unit">
							<view>{{ currentDate }}</view>
							<!-- uni-calendar ref="calendar" :insert="false" :clearDate="true" @change="changeCalendar"
								@confirm="calendarConfirm" @close="calendarClose" /> -->
								<u-calendar :show="calendarshow" :mode="mode" @confirm="calendarConfirm" @close="calendarClose"></u-calendar>
						</view>
					</u-col>
					<u-col span="2">
						<view class="data-unit">
							<u-icon name="arrow-right" color="#fff" size="20"></u-icon>
						</view>
					</u-col>
				</u-row>
			</view>
			<!-- 时间 -->
			<view class="content-date">
				<u-row @click="pickerClick" customStyle="padding: 20rpx 10rpx;borderBottom:1px solid #262a35">
					<u-col span="4">
						<view class="data-text">报告时间</view>
					</u-col>
					<u-col span="6">
						<view class="data-unit">
							<view>{{ pickerValue }}</view>
							<u-datetime-picker :show="show" mode="time" @confirm="pickerConfirm"
								@cancel="pickerCancel"></u-datetime-picker>
						</view>
					</u-col>
					<u-col span="2">
						<view class="data-unit">
							<u-icon name="arrow-right" color="#fff" size="20"></u-icon>
						</view>
					</u-col>
				</u-row>
			</view>
			<!-- 表单数据 -->
			<u-line class="u-line" color="#2979ff"></u-line>
			<view class="content-data">
				<u-row v-for="(item, index) in rows" :key="index"
					customStyle="padding: 20rpx 10rpx;borderBottom:1px solid #262a35">
					<u-col span="4">
						<view class="data-text">{{ item.name }}</view>
					</u-col>
					<u-col span="6">
						<u-input :placeholderClass="placeholderClass" :customStyle="{color:'#fff'}"
							:placeholder="placeholder" border="none" v-model="item.value"
							:class="{ 'input-error': item.error }" color="#fff" @change="changeInput" />
					</u-col>
					<u-col span="2">
						<view class="data-dw">{{ item.unit ? item.unit : "" }}</view>
					</u-col>
				</u-row>
			</view>
			<u-button type="primary" size="large" :custom-style="buttonStyle" @click="submit">
				<text>提交</text>
			</u-button>
		</view>
	</view>
</template>

<script>
	import {
		getToday,
		getCurrentTime,
		getCurrentTime2
	} from '@/utils/util';
	import {
		waterqualityAdd
	} from '@/api/waterquality/waterquality';


	export default {
		data() {
			return {
				mode: 'single',
				calendarshow: false,
				show: false,
				pickerValue: getCurrentTime(),
				iconColor: '#f5f5f5',
				iconShow: true,
				currentDate: getCurrentTime2(),
				placeholder: "请输入数值",
				placeholderClass: "placeholder-style",
				inputCustomStyle: {
					color: '#fff',
					backgroundColor: 'rgba(33, 148, 255, 0.1)',
					borderRadius: '6rpx',
					padding: '8rpx 12rpx',
					border: '1px solid rgba(33, 148, 255, 0.3)',
					fontSize: '28rpx'
				},
				rows: []
			};
		},

		onLoad(options) {
			let datTime = options.data;
			let tabList = JSON.parse(options.tabList);
			console.log(tabList, '=tabList');
			if (tabList.models.length > 0) {
				this.rows = tabList.models;
			}
		},

		methods: {
			toggleCalendar() {
				this.$refs.calendar.open();
			},
			calendarConfirm(e) {
				console.log(e);
				this.currentDate = e.fulldate; // 更新currentDate为选中的日期
			},
			calendarClose(e) {
				console.log(e);
				this.calendarshow=false
			},
			changeInput(e) {
				console.log(e, "changeInput");
			},
			submit() {
				const isValid = this.validateInputs();
				if (isValid) {
					const datas = this.rows.map(item => ({
						identifier: item.identifier,
						value: item.value,
						deviceId: item.deviceId,

					}));
					const dataToSubmit = {
						data: {
							// time: `${this.currentDate} ${this.pickerValue}:00`,
							time: this.currentDate,
							datas
						}

					};
					waterqualityAdd(dataToSubmit)
						.then(res => {
							if (res.code == 200) {
								this.rows.forEach(item => {
									item.value = ""; // 清空输入框的值
									
								});
								uni.showToast({
									title: '提交成功',
									icon: 'success',
									duration: 2000,
								});
								uni.navigateBack({
									delta: 1
								});
							}

						})
						.catch(err => {

						})
					console.log("提交数据:", dataToSubmit);
					// 在此处执行提交逻辑，例如通过 axios 发送请求
				} else {
					uni.showToast({
						title: '请填写完整数据',
						icon: 'none',
						duration: 2000
					});
				}
			},
			validateInputs() {
				let isValid = true;
				for (let row of this.rows) {
					if (!row.value) {
						row.error = true;
						isValid = false;
					} else {
						row.error = false;
					}
				}
				return isValid;
			},
			pickerClick() {
				this.show = true;
			},
			pickerConfirm(e) {
				this.pickerValue = e.value; // 确保pickerDate被正确赋值
				console.log(this.pickerValue, 'pickerValue');
				this.show = false;
			},
			pickerCancel() {
				this.show = false;
			}
		}
	};
</script>

<style lang='scss' scoped>
	.arr {
		position: relative;
		height: 100vh;
		overflow: hidden;
		background: linear-gradient(135deg, rgba(18, 34, 46, 0.95) 0%, rgba(18, 34, 46, 0.85) 100%);
	}

	/* 工业风格背景装饰 */
	.tech-lines {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		pointer-events: none;
		z-index: 0;
	}

	.line {
		position: absolute;
		background: linear-gradient(90deg, transparent, rgba(33, 148, 255, 0.1), transparent);
		height: 1px;
		width: 100%;
		animation: lineMove 3s linear infinite;
	}

	.line-1 { top: 20%; }
	.line-2 { top: 50%; }
	.line-3 { top: 80%; }

	@keyframes lineMove {
		0% { transform: translateX(-100%); }
		100% { transform: translateX(100%); }
	}

	.content {
		padding-top: 20rpx;
		position: relative;
		z-index: 1;
	}

	/* 标题区域 */
	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 20rpx 20rpx;
	}

	.title-box {
		display: flex;
		flex-direction: column;
		gap: 8rpx;
	}

	.title-text {
		font-size: 28rpx;
		color: #fff;
		font-weight: bold;
		text-shadow: 0 0 10rpx rgba(33, 148, 255, 0.5);
	}

	.title-line {
		width: 60rpx;
		height: 3rpx;
		background: linear-gradient(90deg, #2194FF, transparent);
		animation: lineGlow 2s ease-in-out infinite;
	}

	@keyframes lineGlow {
		0%, 100% { opacity: 0.5; }
		50% { opacity: 1; }
	}

	.data-count {
		padding: 4rpx 16rpx;
		background: rgba(33, 148, 255, 0.1);
		border: 1px solid rgba(33, 148, 255, 0.3);
		border-radius: 20rpx;
	}

	.count-text {
		font-size: 20rpx;
		color: #2194FF;
	}

	/* 日期时间选择区域 */
	.datetime-section {
		margin: 20rpx;
		padding: 15rpx;
		background: rgba(18, 34, 46, 0.5);
		border-radius: 12rpx;
		border: 1px solid rgba(33, 148, 255, 0.2);
		display: flex;
		flex-direction: column;
		gap: 16rpx;
	}

	.datetime-row {
		width: 100%;
	}

	.datetime-card {
		position: relative;
		padding: 20rpx;
		background: rgba(33, 148, 255, 0.05);
		border-radius: 8rpx;
		transition: all 0.3s ease;
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;
		box-sizing: border-box;
	}

	.datetime-card:active {
		background: rgba(33, 148, 255, 0.1);
		transform: scale(0.98);
	}

	.datetime-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 100%;
	}

	.datetime-text {
		font-size: 28rpx;
		padding-left: 5rpx;
		font-family: MiSans;
		color: #fff;
		text-shadow: 0 0 10rpx rgba(33, 148, 255, 0.3);
		flex: 1;
		text-align: left;
		margin-right: 20rpx;
	}

	.datetime-unit {
		display: flex;
		align-items: center;
		gap: 8rpx;
		min-width: 120rpx;
	}

	.datetime-unit-value {
		color: #fff;
		font-family: 'Courier New', monospace;
		text-shadow: 0 0 10rpx rgba(33, 148, 255, 0.3);
		font-size: 30rpx;
		text-align: right;
	}

	.datetime-arrow {
		margin-left: 10rpx;
	}

	/* 数据录入区域 */
	.content-data {
		margin: 20rpx;
		padding: 15rpx;
		background: rgba(18, 34, 46, 0.5);
		border-radius: 12rpx;
		border: 1px solid rgba(33, 148, 255, 0.2);
		width: calc(100% - 40rpx);
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
		gap: 16rpx;
	}

	.data-row {
		animation: slideIn 0.5s ease-out forwards;
		opacity: 0;
		transform: translateY(20rpx);
		width: 100%;
	}

	@keyframes slideIn {
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.data-card {
		position: relative;
		padding: 20rpx;
		background: rgba(33, 148, 255, 0.05);
		border-radius: 8rpx;
		transition: all 0.3s ease;
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;
		box-sizing: border-box;
	}

	.data-card.input-error {
		border: 1px solid #ff6b35;
		background: rgba(255, 107, 53, 0.1);
	}

	.card-corner {
		position: absolute;
		width: 10rpx;
		height: 10rpx;
		border: 1px solid rgba(33, 148, 255, 0.5);
	}

	.top-left {
		top: 0;
		left: 0;
		border-right: none;
		border-bottom: none;
	}

	.top-right {
		top: 0;
		right: 0;
		border-left: none;
		border-bottom: none;
	}

	.bottom-left {
		bottom: 0;
		left: 0;
		border-right: none;
		border-top: none;
	}

	.bottom-right {
		bottom: 0;
		right: 0;
		border-left: none;
		border-top: none;
	}

	.data-text {
		font-size: 28rpx;
		padding-left: 5rpx;
		font-family: MiSans;
		color: #fff;
		text-shadow: 0 0 10rpx rgba(33, 148, 255, 0.3);
		flex: 1;
		text-align: left;
		margin-right: 20rpx;
	}

	.data-unit {
		display: flex;
		align-items: center;
		gap: 8rpx;
		min-width: 200rpx;
	}

	.input-wrapper {
		flex: 1;
		min-width: 150rpx;
	}

	.custom-span {
		font-size: 24rpx;
		color: #fff;
		margin-left: 4rpx;
		min-width: 40rpx;
	}

	/* 提交按钮 */
	.btn {
		position: relative;
		width: 60%;
		margin: 40rpx auto 20rpx;
		overflow: hidden;
	}

	.btn-content {
		color: rgb(255, 255, 255);
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		border-radius: 16rpx;
		background: linear-gradient(135deg, rgba(33, 148, 255, 0.2), rgba(33, 148, 255, 0.1));
		border: 1px solid rgba(33, 148, 255, 0.3);
		transition: all 0.3s ease;
		font-size: 30rpx;
		font-weight: bold;
	}

	.btn-border {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		border-radius: 16rpx;
		background: linear-gradient(90deg, transparent, rgba(33, 148, 255, 0.5), transparent);
		opacity: 0;
		transition: opacity 0.3s ease;
		pointer-events: none;
	}

	.btn:active .btn-content {
		transform: scale(0.98);
		background: linear-gradient(135deg, rgba(33, 148, 255, 0.3), rgba(33, 148, 255, 0.2));
	}

	.btn:active .btn-border {
		opacity: 1;
	}

	.data-text {
		font-size: 28rpx;
		padding-left: 5rpx;
		font-family: MiSans;
		color: #fff;
		text-shadow: 0 0 10rpx rgba(33, 148, 255, 0.3);
	}

	.data-unit {
		color: #fff;
		font-family: 'Courier New', monospace;
		text-shadow: 0 0 10rpx rgba(33, 148, 255, 0.3);
		font-size: 30rpx;
	}

	.data-dw {
		font-size: 24rpx;
		color: #fff;
		margin-left: 4rpx;
	}

	/* 日历组件样式覆盖 */
	::v-deep .u-calendar {
		.u-calendar-header__title,
		.u-calendar-header__subtitle {
			color: #fff !important;
		}
		
		.u-calendar-month__days__day {
			color: #fff !important;
		}
	}

	/* 时间选择器样式覆盖 */
	::v-deep .u-datetime-picker {
		.u-picker__header__title {
			color: #fff !important;
		}
		
		.u-picker-column-item {
			color: #fff !important;
		}
	}
</style>
