@font-face {
  font-family: "iconfont";
  src: url('@/static/font/iconfont.ttf') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  display: inline-block;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-user:before {
  content: "\e7ae";
}

.icon-password:before {
  content: "\e8b2";
}

.icon-code:before {
  content: "\e699";
}

.icon-setting:before {
  content: "\e6cc";
}

.icon-share:before {
  content: "\e739";
}

.icon-edit:before {
  content: "\e60c";
}

.icon-version:before {
  content: "\e63f";
}

.icon-service:before {
  content: "\e6ff";
}

.icon-friendfill:before {
  content: "\e726";
}

.icon-community:before {
  content: "\e741";
}

.icon-people:before {
  content: "\e736";
}

.icon-dianzan:before {
  content: "\ec7f";
}

.icon-right:before {
  content: "\e7eb";
}

.icon-logout:before {
  content: "\e61d";
}

.icon-help:before {
  content: "\e616";
}

.icon-github:before {
  content: "\e628";
}

.icon-aixin:before {
  content: "\e601";
}

.icon-clean:before {
  content: "\e607";
}

.icon-refresh:before {
  content: "\e604";
}

[class*="baseicon-"] {
	font-family: "baseicon";
	font-size: inherit;
	font-style: normal;
}

@font-face {
	  font-family: 'baseicon';  /* Project id 3971655 */
	src: url('//at.alicdn.com/t/c/font_3971655_eqn3bxnai4m.woff2?t=1681454443831') format('woff2'),
	       url('//at.alicdn.com/t/c/font_3971655_eqn3bxnai4m.woff?t=1681454443831') format('woff'),
	       url('//at.alicdn.com/t/c/font_3971655_eqn3bxnai4m.ttf?t=1681454443831') format('truetype');
	}

.baseicon-signal:before {
	content: "\e76c";
}

.baseicon-no-signal:before {
	content: "\e76b";
}

.baseicon-kaiguan:before {
	content: "\e661";
}

.baseicon-notice:before {
	content: "\e605";
}

.baseicon-collect:before {
	content: "\e8c2";
}

.baseicon-no-collect:before {
	content: "\e659";
}

.baseicon-rightarrow:before {
	content: "\e628";
}

.baseicon-device:before {
	content: "\e651";
}

.baseicon-operate:before {
	content: "\e64f";
}

.baseicon-system:before {
	content: "\e62c";
}

.baseicon-coalscuttle:before {
	content: "\e68a";
}

.baseicon-microwaveoven:before {
	content: "\e68b";
}

.baseicon-tv:before {
	content: "\e690";
}

.baseicon-icebox:before {
	content: "\e691";
}

.baseicon-electricfan:before {
	content: "\e694";
}

.baseicon-sound:before {
	content: "\e697";
}

.baseicon-more:before {
	content: "\e615";
}

.baseicon-add:before {
	content: "\e61d";
}

.baseicon-qrcode:before {
	content: "\e633";
}

.baseicon-delete:before {
	content: "\e718";
}

.baseicon-edit:before {
	content: "\e601";
}

.baseicon-addsign:before {
	content: "\e64d";
}

