<template>
	<view class="info-page">
		<!-- 使用AppHeader组件 -->
		<AppHeader flex iconShow title="个人信息" :bg="bg" />

		<view class="container">
			<!-- 信息列表 -->
			<view class="info-list">
				<view class="info-item">
					<view class="info-label">
						<text class="label-icon">👤</text>
						<text class="label-text">昵称</text>
					</view>
					<view class="info-value">
						<text class="value-text">{{ user.nickName || '未设置' }}</text>
					</view>
				</view>

				<view class="info-item">
					<view class="info-label">
						<text class="label-icon">📱</text>
						<text class="label-text">手机号码</text>
					</view>
					<view class="info-value">
						<text class="value-text">{{ user.phonenumber || '未绑定' }}</text>
					</view>
				</view>

				<view class="info-item">
					<view class="info-label">
						<text class="label-icon">📧</text>
						<text class="label-text">邮箱</text>
					</view>
					<view class="info-value">
						<text class="value-text">{{ user.email || '未设置' }}</text>
					</view>
				</view>

				<view class="info-item">
					<view class="info-label">
						<text class="label-icon">💼</text>
						<text class="label-text">岗位</text>
					</view>
					<view class="info-value">
						<text class="value-text">{{ postGroup || '未分配' }}</text>
					</view>
				</view>

				<view class="info-item">
					<view class="info-label">
						<text class="label-icon">👥</text>
						<text class="label-text">角色</text>
					</view>
					<view class="info-value">
						<text class="value-text">{{ roleGroup || '未分配' }}</text>
					</view>
				</view>

				<view class="info-item">
					<view class="info-label">
						<text class="label-icon">📅</text>
						<text class="label-text">创建日期</text>
					</view>
					<view class="info-value">
						<text class="value-text">{{ user.createTime || '未知' }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getUserProfile
	} from "@/api/system/user"
	import {
		changeTimesStamp
	} from "@/utils/util"

	export default {
		data() {
			return {
				user: {},
				roleGroup: "",
				postGroup: "",
				bg: '#0a0e1a'
			}
		},
		onLoad() {
			this.getUser()
		},
		methods: {
			// 获取用户信息
			getUser() {
				getUserProfile().then(res => {
					this.user = res.data.user
					this.user.createTime = changeTimesStamp(res.data.user.createTime)
					this.roleGroup = res.roleGroup
					this.postGroup = res.postGroup
				})
			}
		}
	}
</script>

<style scoped>
/* 主容器 */
.info-page {
	height: 100vh; /* 使用固定高度而不是最小高度 */
	background: linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 50%, #0f1419 100%);
	overflow: hidden; /* 防止主容器滚动 */
}

/* 容器 */
.container {
	height: calc(100vh - 120rpx); /* 为AppHeader留出空间 */
	padding: 30rpx 20rpx;
	overflow-y: auto; /* 内容超出时才滚动 */
}

/* 信息列表 */
.info-list {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

/* 信息项 */
.info-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 25rpx;
	background: linear-gradient(135deg, #1a2332 0%, #0f1419 100%);
	border: 1rpx solid #2a3441;
	border-radius: 15rpx;
	transition: all 0.3s ease;
}

.info-item:active {
	transform: scale(0.98);
	background: linear-gradient(135deg, #2a3441 0%, #1a2332 100%);
}

/* 标签区域 */
.info-label {
	display: flex;
	align-items: center;
	flex: 1;
}

.label-icon {
	font-size: 28rpx;
	margin-right: 15rpx;
}

.label-text {
	font-size: 28rpx;
	color: #ffffff;
	font-weight: 500;
}

/* 值区域 */
.info-value {
	flex: 1;
	text-align: right;
}

.value-text {
	font-size: 26rpx;
	color: #cccccc;
	word-break: break-all;
}

/* 响应式设计 */
@media (max-width: 320px) {
	.header-title {
		font-size: 28rpx;
	}

	.label-text {
		font-size: 24rpx;
	}

	.value-text {
		font-size: 22rpx;
	}
}
</style>