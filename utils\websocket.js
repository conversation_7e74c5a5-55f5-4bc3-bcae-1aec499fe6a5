let socketMsgQueue = [];
let socketOpen = false
import Stomp from 'stompjs'
import config from '@/config'

// 生产 ws://CIMS.waterme.cn/prod-api/chat-app
// 开发 ws://120.197.117.244:11883/dev-api/chat-app

export default {
    socketOpen: false,
    client: null,
    baseURL: config.baseUrl.replace("http", "ws") +"/chat-app",
    header:{
        login: 'mylogin',
        passcode: 'mypasscode',
        Authorization: "deviceiiita"
    },
    init() {
        var that = this
        if (this.client) {
            return Promise.resolve(this.client);
        }

        return new Promise((resolve, reject) => {
            const ws = {
                send: this.sendMessage,
                onopen: null,
                onmessage: null,
                close: this.closeSocket,
            };

            uni.connectSocket({
                url: this.baseURL,
                header: this.header,
                success: (res) => {
                    console.log(res)
                },
                fail: (err) => {
                    console.log(err)
                }
            });

            uni.onSocketOpen(function(res) {
                console.log('WebSocket连接已打开！', res);

                socketOpen = true
                that.socketOpen = true;

                for (let i = 0; i < socketMsgQueue.length; i++) {
                    ws.send(socketMsgQueue[i]);
                }
                socketMsgQueue = [];

                ws.onopen && ws.onopen();
            });

            uni.onSocketMessage(function(res) {
                ws.onmessage && ws.onmessage(res);
            });

            uni.onSocketError(function(res) {
                console.log('WebSocket 错误！', res);
                uni.showModal({
                    title: '提示',
                    content: '设备连接失败',
                    showCancel: false,
                    success(res) {
                        if (res.confirm) {
                            uni.navigateBack({
                                delta: 3
                            })
                        }
                    }
                })
            });

            // const Stomp = require('./stomp.js').Stomp;
            uni.onSocketClose((res) => {
                this.client.disconnect();
                this.client = null;
                socketOpen = false
                that.socketOpen = false;
                console.log('WebSocket 已关闭！', res);

                uni.$emit('socketClose')
            });

            Stomp.setInterval = function(interval, f) {
                return setInterval(f, interval);
            };

            Stomp.clearInterval = function(id) {
                return clearInterval(id);
            };

            const client = this.client = Stomp.over(ws);
            client.connect(this.header, function() {
                console.log('stomp connected');
                resolve(client);
            });
        });
    },
    disconnect() {
        if (socketOpen) {
            uni.closeSocket();
        }
    },
    sendMessage(message) {
        if (socketOpen) {
            uni.sendSocketMessage({
                data: message,
                success: (res) => {
                    console.log(res)
                },
                fail: (err) => {
                    console.log(err)
                }
            });
        } else {
            socketMsgQueue.push(message);
        }
    },
    closeSocket() {
        console.log('closeSocket');
    },
};
