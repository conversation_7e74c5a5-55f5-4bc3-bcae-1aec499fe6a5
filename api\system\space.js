import request from '@/utils/request'


export function getSpaces(homeId) {
  return request({
    url: '/space/getSpaces',
    method: 'Post',
    data: {
      data: homeId
    }
  })
}

export function addSpace(data) {
  return request({
    url: '/space/addSpace',
    method: 'post',
	data: {
      data
    }
  })
}

export function saveSpace(data) {
  return request({
    url: '/space/saveSpace',
    method: 'post',
	data: {
      data
    }
  })
}

export function delSpace(id) {
  return request({
    url: '/space/delSpace',
    method: 'Post',
    data: id
  })
}

export function getCurrentHome() {
  return request({
    url: '/space/currentHome',
    method: 'Post'
  })
}

export function changCurrentHome(data) {
  return request({
    url: '/space/changCurrentHome',
    method: 'post',
	params: data
  })
}

export function getUserHomes() {
  return request({
    url: '/space/getUserHomes',
    method: 'Post'
  })
}

export function getDevicesBySpaceId(spaceId) {
  return request({
    url: '/space/getSpaceDevices',
    method: 'Post',
    data: {
      data: spaceId
    }
  })
}
