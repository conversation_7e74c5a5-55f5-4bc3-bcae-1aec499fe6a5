<template>
	<view class="zt">
		<AppHeader flex title="[CIMS]告警详情"/>
		<view class="secetDate">
			<uni-datetime-picker 
				:show="pickerShow" 
				@change="getdate" 
				v-model="datetimerange" 
				:border="false" 
				:clear-icon="false" 
				:hide-second="true"  
				@click="changePicker" 
				type="datetimerange" 
				rangeSeparator="至" />
		</view>
		<scroll-view
			class="arr"
			scroll-y
			lower-threshold="50"
			@scrolltolower="loadMore"
		>
			<view class="content-data" v-for="(item, index) in alarmRecordList" :key="index">
				<u-row>
					<u-col span="6">
						<view class="data-unit2">点位：{{item.propertyName?item.propertyName:''}}</view>
					</u-col>
					<u-col span="6">
						<view class="data-unit1">告警值：{{item.value?item.value:""}}</view>
					</u-col>
				</u-row>
				<u-row>
					<u-col span="6">
						<view class="data-unit2">告警类型：{{getTypeName(item.level)?getTypeName(item.level):getTypeName(item.level)}}</view>
					</u-col>
					<u-col span="6">
						<view class="data-unit1">告警时间：{{item.createTime?item.createTime:''}}</view>
					</u-col>
				</u-row>
			</view>
			<view v-if="loading" class="loading">加载中...</view>
			<view v-if="noMoreData" class="no-more-data">没有更多数据了</view>
		</scroll-view>
	</view>
</template>


<script>
	import {
		alarmRecord
	} from '@/api/alarm/alarm.js'
	import {
		getProject
	} from "@/utils/auth.js";
	export default {
		data() {
			return {
				// bg: '#131b2e',
				iconColor: '#f5f5f5',
				iconShow: true,
				pickerShow: false,
				datetimerange: [this.getDefaultStartDate(), this.getDefaultEndDate()],
				alarmList: {},
				alarmType: [],
				project: {},
				alarmRecordList: [],
				pageNum: 1,
				pageSize: 10,
				loading: false,
				noMoreData: false,
			}
		},
		methods: {
			getTypeName(type) {
				const typeStr = type.toString();
				const typeObj = this.alarmType.find(item => item.id.toString() === typeStr);
				return typeObj ? typeObj.name : '';
			},
			async getalarmRecordList() {
				if (this.loading) return;
				this.loading = true;
				this.project = getProject();
				const timesWithSeconds = this.datetimerange.map(time => time + ":00");
				console.log(this.alarmList,'========');
				const params = {
					pageNum: this.pageNum,
					pageSize: this.pageSize,
					data: {
						projectId: this.project.id,
						deviceId: this.alarmList.deviceId,
						identifier: this.alarmList.identifier,
						level:this.alarmList.level,
						params: {
							startTime: timesWithSeconds[0],
							endTime: timesWithSeconds[1]
						}
					}
				}
				try {
					const res = await alarmRecord(params);
					console.log(res, 'alarmRecord');
					if (res.data.rows.length > 0) {
						this.alarmRecordList = [...this.alarmRecordList, ...res.data.rows];
						this.pageNum += 1;
					} else {
						this.noMoreData = true;
					}
				} catch (err) {
					console.log(err, 'alarmRecord');
				}
				this.loading = false;
			},
			clkPicker() {
				this.pickerShow = true
			},
			changePicker() {
				this.pickerShow = !this.pickerShow;
			},
			getdate(e) {
				this.datetimerange = e;
				console.log(this.datetimerange, 'selected date range');
				this.resetData();
				this.getalarmRecordList();
			},
			resetData() {
				this.alarmRecordList = [];
				this.pageNum = 1;
				this.noMoreData = false;
			},
			getDefaultStartDate() {
				const today = new Date();
				const futureDate = new Date(today);
				futureDate.setDate(today.getDate() - 7);
				return this.formatDate(futureDate);
			},
			getDefaultEndDate() {
				const today = new Date();
				return this.formatDate(today);
			},
			formatDate(date) {
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				return `${year}-${month}-${day} ${hours}:${minutes}`;
			},
			loadMore() {
				if (!this.noMoreData) {
					this.getalarmRecordList();
				}
			}
		},
		onLoad: function (option) {
			const dataString = option.data;
			console.log(dataString, 'dataString-----');
			const List = JSON.parse(dataString);
			console.log(List, '-------------------');
			this.alarmList = List.data;
			this.alarmType = List.alarmType;
			this.getalarmRecordList();
		},
	}
</script>

<style scoped>
	.zt{
		min-height: 100vh;
		/* background: linear-gradient(0deg, #1c3853, #152034); */
		overflow: auto;
	}
	.arr {
		height: calc(100vh - 100rpx); /* Adjust this value based on the height of the fixed header and date picker */
	}

	.secetDate {
		position: sticky;
		top: 0;
		z-index: 1000; /* Ensure it stays on top of other content */
		background-color: #131b2e; /* Match the background color to blend with the header */
		padding: 15rpx;
	}

	.content-data {
		margin: 15rpx 15rpx;
		padding: 15rpx;
		background-color: #2d4a64;
		color: #f5f5f5;
		border-radius: 15rpx;
	}

	.data-unit1,
	.data-unit2 {
		color: #f5f5f5;
		font-size: 30rpx;
	}

	.data-unit2 {
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.loading,
	.no-more-data {
		text-align: center;
		color: #f5f5f5;
		font-size: 28rpx;
		padding: 20rpx 0;
	}
	::v-deep .uni-date-x{
		background-color: #2d4a64;
		color:#f5f5f5;
	}
	::v-deep .uni-date-x__header__icon{
		color:#f5f5f5;
	}
	::v-deep .uni-date-x__header__icon--right{
		color:#f5f5f5;
	}
	::v-deep .uni-date-x__header__icon--left{
		color:#f5f5f5;
	}
	::v-deep .uni-date-x__header__btn{
		color:#f5f5f5;
	}
</style>

