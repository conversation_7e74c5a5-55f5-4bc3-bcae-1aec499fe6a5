<template>
	<view class="chart-container">
		<qiun-data-charts 
			v-if="chartData"
			type="area" 
			:chartData="qiunChartData" 
			:opts="qiunOpts" 
			:ontouch="true" 
			:loadingType="0"
			background="rgba(9, 20, 28, 0.9)" 
			canvas2d
			class="chart-canvas">
		</qiun-data-charts>
		<view v-else class="chart-empty">
			<text>暂无数据</text>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'SimpleChart',
		props: {
			chartData: {
				type: Object,
				default: () => null
			},
			height: {
				type: String,
				default: '400rpx'
			}
		},
		data() {
			return {
				qiunChartData: {
					categories: [],
					series: []
				},
				qiunOpts: {
					fontColor: "#FFFFFF",
					fontSize: 11,
					color: ["#2194FF", "#00ff88", "#ff6b35"],
					touchMoveLimit: 25,
					padding: [15, 10, 15, 10],
					dataLabel: false,
					dataPointShape: true,
					dataPointShapeType: "circle",
					legend: {
						show: false
					},
					enableScroll: true,
					xAxis: {
						disableGrid: false,
						itemCount: 8,
						scrollShow: true,
						scrollBackgroundColor: 'rgba(8, 43, 66, 0.8)',
						scrollColor: 'rgba(6, 64, 96, 0.8)',
						axisLineColor: 'rgba(255, 255, 255, 0.3)',
						fontColor: 'rgba(255, 255, 255, 0.8)',
						fontSize: 10,
						scrollAlign: 'right',
						rotateLabel: true,
						rotateAngle: 45
					},
					yAxis: {
						gridType: "dash",
						dashLength: 2,
						fontColor: 'rgba(255, 255, 255, 0.8)',
						fontSize: 10,
						showTitle: true,
						gridColor: "rgba(255, 255, 255, 0.15)",
						data: [{
							title: '',
							titleFontColor: "#00ff88",
							titleFontSize: 12,
							titleOffsetY: -5,
							titleOffsetX: 280
						}]
					},
					extra: {
						area: {
							type: "curve",
							opacity: 0.3,
							width: 3,
							activeType: "hollow",
							linearType: "custom",
							onShadow: true,
							animation: "horizontal",
							connectNulls: false
						}
					}
				}
			};
		},
		watch: {
			chartData: {
				handler(newData) {
					if (newData) {
						this.updateChartData(newData);
					}
				},
				deep: true,
				immediate: true
			}
		},
		methods: {
			updateChartData(data) {
				// 强制创建新的数据对象，确保响应式更新
				this.qiunChartData = {
					categories: data.xdata || [],
					series: []
				};

				// 处理系列数据，保持null值以实现断点效果
				if (data.series && data.series.length > 0) {
					this.qiunChartData.series = data.series.map(serie => ({
						name: serie.name,
						data: serie.data, // 保持原始数据，包括null值
						connectNulls: serie.connectNulls !== undefined ? serie.connectNulls : false // 支持断点
					}));
				}

				// 动态设置图例显示
				if (data.showLegend !== undefined) {
					this.qiunOpts.legend.show = data.showLegend;
				} else {
					// 当有多个系列时自动显示图例
					this.qiunOpts.legend.show = data.series && data.series.length > 1;
				}

				// 更新Y轴标题
				if (data.unit) {
					this.qiunOpts.yAxis.data[0].title = data.unit;
				}

				// 设置x轴显示数量
				const dataLength = data.xdata ? data.xdata.length : 0;
				this.qiunOpts.xAxis.itemCount = dataLength > 12 ? 8 : Math.max(dataLength, 5);
			}
		}
	};
</script>

<style scoped>
	.chart-container {
		width: 100%;
		height: 400rpx;
		position: relative;
	}
	
	.chart-canvas {
		width: 100%;
		height: 100%;
	}
	
	.chart-empty {
		width: 100%;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		color: rgba(255, 255, 255, 0.5);
		font-size: 28rpx;
	}
</style> 