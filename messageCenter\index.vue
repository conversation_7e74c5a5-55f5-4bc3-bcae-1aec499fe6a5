<template>
	<view class="arr industrial-container">
		<AppHeader flex title="[CIMS]消息中心" />
		<!-- 筛选器容器 -->
		<view class="selectors tech-panel">
			<view class="select-item">
				<view class="select-decorator"></view>
				<uni-datetime-picker :show="pickerShow" @change="getdate" v-model="datetimerange" hide-second="false"
					@click="changePicker" type="datetimerange" class="custom-select">{{ formattedDateRange }}</uni-datetime-picker>
			</view>
			<view class="select-item">
				<view class="select-decorator"></view>
				<uni-data-select v-model="readVal" :clear="false" :localdata="readList" @change="changeRead"
					class="custom-select"></uni-data-select>
			</view>
		</view>

		<!-- 消息统计面板 -->
		<view class="message-stats tech-panel">
			<view class="stat-item">
				<view class="stat-value">{{msgData.length}}</view>
				<view class="stat-label">总消息数</view>
				<view class="pulse-dot"></view>
			</view>
			<view class="stat-item">
				<view class="stat-value">{{getUnreadCount}}</view>
				<view class="stat-label">未读</view>
				<view class="pulse-dot warning"></view>
			</view>
			<view class="stat-item">
				<view class="stat-value">{{getReadCount}}</view>
				<view class="stat-label">已读</view>
				<view class="pulse-dot success"></view>
			</view>
		</view>

		<scroll-view class="scroll-content" scroll-y="true" @scrolltolower="loadMore">
			<!-- 空数据组件 -->
			<EmptyData
				:show="msgData.length === 0 && !loading"
				title="暂无消息"
				description="当前没有新的消息通知"
			/>

			<view v-for="(data, index) in msgData" :key="index" class="content-data tech-card">
				<view class="tech-card-header">
					<view class="header-left">
						<view class="status-indicator" :class="getReadStatusClass(data.readFlag)"></view>
						<view class="message-type">{{getMessageTypeText(data.messageType)}}</view>
					</view>
					<view class="header-right" :class="data.readFlag === 0 ? 'unread' : 'read'">
						<view class="status-text">{{ data.readFlag === 0 ? '未读' : '已读' }}</view>
						<view class="corner-decorator"></view>
					</view>
				</view>

				<view class="tech-card-content">
					<view class="title-row">
						<view class="message-title">{{ data.title ? data.title : "无标题" }}</view>
					</view>

					<view class="content-row">
						<view class="message-content">
							<view v-if="data.content" v-html="data.content"></view>
							<view v-else>暂无消息内容</view>
						</view>
					</view>

					<view class="time-info">
						<view class="time-row">
							<text class="time-label">创建时间</text>
							<text class="time-value">{{ data.createTime ? data.createTime : "" }}</text>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>

		<view v-if="loading" class="loading tech-loading">
			<view class="loading-circle"></view>
			<text>加载中...</text>
		</view>
		<view v-if="noMoreData" class="no-more-data">没有更多数据</view>
	</view>
</template>

<script>
	import {
		MessageList,
		MessageRead
	} from "@/api/messageCenter/api.js";
	import {
		getProject
	} from "@/utils/auth.js";
	import {
		alarmDict
	} from "@/api/alarm/alarm.js";
	import EmptyData from '@/components/EmptyData/EmptyData.vue';

	export default {
		components: {
			EmptyData
		},
		data() {
			return {
				single: "",
				datetimerange: [this.getDefaultStartDate(), this.getDefaultEndDate()],
				// bg: '#131b2e',
				iconColor: "#f5f5f5",
				iconShow: true,
				msgData: [],
				type: [],
				project: {},
				readList: [{
						value: 1,
						text: "已读",
					},
					{
						value: 0,
						text: "未读",
					},
				],
				dictTypeVal: "",
				readVal: 0, // 默认未读
				pageNum: 1,
				pageSize: 10, // 每次加载10条数据
				hasMoreData: true,
				loading: false,
				noMoreData: false,
				pickerShow: false,
				dictType: [],
			};
		},
		computed: {
			formattedDateRange() {
				console.log(this.datetimerange, "---datetimerange");
				return this.datetimerange.join(" ~ ");
			},
			// 获取未读消息数量
			getUnreadCount() {
				return this.msgData.filter(item => item.readFlag === 0).length;
			},
			// 获取已读消息数量
			getReadCount() {
				return this.msgData.filter(item => item.readFlag === 1).length;
			}
		},
		onPullDownRefresh(){
			console.log('消息中心页面下拉刷新触发');
			// 重置页面数据
			this.pageNum = 1;
			this.msgData = [];
			this.hasMoreData = true;
			this.noMoreData = false;
			
			// 刷新数据
			this.getAlarmDict().then(() => {
				this.getMessageList();
			}).finally(() => {
				setTimeout(() => {
					uni.stopPullDownRefresh();
				}, 300);
			});
		},
		mounted() {
			
		},
		onShow() {
			this.getMessageList();
		},
		methods: {
			// 获取读取状态样式类
			getReadStatusClass(readFlag) {
				return readFlag === 0 ? 'unread' : 'read';
			},
			getAlarmDict() {
				return new Promise((resolve, reject) => {
					const params = {
						data: "notify_message_type",
						pageNum: 1,
						pageSize: 30,
					};
					alarmDict(params)
						.then((res) => {
							if (res.data.length > 0) {
								this.dictType = res.data.map((item) => {
									return {
										text: item.dictLabel,
										value: item.dictValue,
									};
								});
								resolve(); // 成功加载字典数据，调用resolve
							} else {
								console.warn("No dictionary data returned.");
								resolve(); // 即使没有数据也调用resolve，以防止卡住
							}
						})
						.catch((err) => {
							console.error("Failed to load dictionary data:", err);
							reject(err); // 加载失败，调用reject
						});
				});
			},

			// 新增方法，用于根据messageType获取对应的text
			getMessageTypeText(messageType) {
				// 检查dictType是否存在数据
				if (!this.dictType || this.dictType.length === 0) {
					return "暂无消息类型"; // 或者返回一个默认的文本
				}
				const typeStr = messageType.toString();
				const typeObj = this.dictType.find((item) => item.value === typeStr);
				return typeObj ? typeObj.text : "暂无消息类型"; // 找不到匹配类型时返回默认文本
			},
			changePicker() {
				this.pickerShow = !this.pickerShow;
			},
			getdate(e) {
				this.datetimerange = e;
				this.getMessageList();
			},
			getDefaultStartDate() {
				const today = new Date();
				const futureDate = new Date(today);
				futureDate.setDate(today.getDate() - 7);
				return this.formatDate(futureDate);
			},
			getDefaultEndDate() {
				const today = new Date();
				return this.formatDate(today);
			},
			formatDate(date) {
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, "0");
				const day = String(date.getDate()).padStart(2, "0");
				const hours = String(date.getHours()).padStart(2, "0");
				const minutes = String(date.getMinutes()).padStart(2, "0");
				return `${year}-${month}-${day} ${hours}:${minutes}`;
			},
			getMessageList() {
				if (this.loading) return;
				this.loading = true;
				// 确保dictType加载完成
				if (!this.dictType || this.dictType.length === 0) {
					console.warn(
						"Dictionary data not loaded, delaying message list request."
					);
					this.getAlarmDict().then(() => {
						this.getMessageList(); // 再次调用加载消息列表的方法
					});
					this.loading = false;
					return;
				}

				this.project = getProject();
				const datetimerangeWithSeconds = this.datetimerange.map((datetime) => {
					return datetime + ":00";
				});
				const params = {
					pageNum: this.pageNum,
					pageSize: this.pageSize,
					data: {
						readFlag: this.readVal,
						messageType: this.messageType,
						projectId: this.project.id,
					},
					params: {
						beginTime: datetimerangeWithSeconds[0],
						endTime: datetimerangeWithSeconds[1],
					},
				};
				MessageList(params)
					.then((res) => {
						if (res.data.rows.length > 0) {
							this.msgData = this.msgData.concat(res.data.rows);
							this.hasMoreData = res.data.rows.length >= this.pageSize;
							this.noMoreData = false;
							const msgIds = {
								data: res.data.rows.map((item) => item.id),
							};
							MessageRead(msgIds).then((res) => {
								if (res.code == 200) {
									console.log("12312312");
								}
							});
						} else {
							if (this.pageNum === 1) {
								this.msgData = [];
							}
							this.hasMoreData = false;
							this.noMoreData = true;
						}
						this.loading = false;
					})
					.catch((err) => {
						this.loading = false;
						console.error(err);
					});
			},
			getreadFlag(type) {
				const typeStr = type.toString();
				const typeObj = this.readList.find(
					(item) => item.value.toString() === typeStr
				);
				return typeObj ? typeObj.text : "";
			},
			changeRead(value) {
				this.readVal = value;
				this.resetList();
			},
			clearSelection() {
				this.readVal = ""; //
				this.resetList();
			},
			resetList() {
				this.pageNum = 1;
				this.hasMoreData = true;
				this.noMoreData = false;
				this.msgData = [];
				this.getMessageList();
			},
			loadMore() {
				console.log("loadMore called");
				if (this.hasMoreData) {
					this.pageNum++;
					this.getMessageList();
				}
			},
		},
	};
</script>

<style scoped>
.industrial-container {
	background: linear-gradient(180deg, rgba(16, 24, 36, 0.95) 0%, rgba(12, 20, 28, 0.98) 100%);
	position: relative;
	display: flex;
	flex-direction: column;
	height: 100vh;
}

.industrial-container::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background:
		repeating-linear-gradient(0deg, transparent 0px, rgba(33, 148, 255, 0.03) 1px, transparent 2px),
		repeating-linear-gradient(90deg, transparent 0px, rgba(33, 148, 255, 0.03) 1px, transparent 2px);
	background-size: 20px 20px;
	pointer-events: none;
}

.tech-panel {
	background: rgba(18, 34, 46, 0.7);
	border: 1px solid rgba(33, 148, 255, 0.3);
	border-radius: 8px;
	padding: 15rpx;
	position: relative;
	margin: 20rpx;
}

.tech-panel::before {
	content: '';
	position: absolute;
	top: -2px;
	left: -2px;
	right: -2px;
	bottom: -2px;
	background: linear-gradient(90deg, rgba(33, 148, 255, 0.3), transparent);
	border-radius: 10px;
	z-index: -1;
}

.selectors {
	display: flex;
	justify-content: space-between;
	align-items: center;
	gap: 20rpx;
	flex-shrink: 0;
}

.select-item {
	flex: 1;
	position: relative;
}

.select-decorator {
	position: absolute;
	width: 6px;
	height: 6px;
	border: 1px solid rgba(33, 148, 255, 0.6);
	border-radius: 1px;
}

.select-decorator:nth-child(1) {
	top: -2px;
	left: -2px;
}

.select-decorator:nth-child(2) {
	top: -2px;
	right: -2px;
}

.message-stats {
	display: flex;
	justify-content: space-around;
	padding: 20rpx;
	flex-shrink: 0;
}

.stat-item {
	text-align: center;
	position: relative;
}

.stat-value {
	font-size: 36rpx;
	color: #2194ff;
	font-weight: bold;
	text-shadow: 0 0 10px rgba(33, 148, 255, 0.3);
}

.stat-label {
	font-size: 24rpx;
	color: #b9c8dc;
	margin-top: 5rpx;
}

.pulse-dot {
	width: 8rpx;
	height: 8rpx;
	border-radius: 50%;
	background: #2194ff;
	position: absolute;
	right: -15rpx;
	top: 50%;
	animation: pulse 2s infinite;
}

.pulse-dot.warning {
	background: #ff6b35;
}

.pulse-dot.success {
	background: #00ff88;
}

@keyframes pulse {
	0% {
		transform: scale(1);
		opacity: 1;
	}
	50% {
		transform: scale(1.5);
		opacity: 0.5;
	}
	100% {
		transform: scale(1);
		opacity: 1;
	}
}

.tech-card {
	background: rgba(18, 34, 46, 0.7);
	border: 1px solid rgba(33, 148, 255, 0.2);
	border-radius: 8px;
	margin: 20rpx;
	padding: 20rpx;
	position: relative;
	transition: all 0.3s ease;
}

.tech-card:hover {
	transform: translateY(-2px);
	box-shadow: 0 4px 12px rgba(33, 148, 255, 0.1);
}

.tech-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 2px;
	background: linear-gradient(90deg, transparent, rgba(33, 148, 255, 0.5), transparent);
}

.tech-card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}

.header-left {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.status-indicator {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
	position: relative;
}

.status-indicator::after {
	content: '';
	position: absolute;
	top: -2px;
	left: -2px;
	right: -2px;
	bottom: -2px;
	border-radius: 50%;
	animation: statusPulse 2s infinite;
}

.status-indicator.unread {
	background: #ff6b35;
}

.status-indicator.read {
	background: #00ff88;
}

@keyframes statusPulse {
	0% {
		box-shadow: 0 0 0 0 rgba(255, 107, 53, 0.4);
	}
	70% {
		box-shadow: 0 0 0 6px rgba(255, 107, 53, 0);
	}
	100% {
		box-shadow: 0 0 0 0 rgba(255, 107, 53, 0);
	}
}

.message-type {
	font-size: 28rpx;
	color: #f5f5f5;
	font-weight: 500;
}

.header-right {
	display: flex;
	align-items: center;
	padding: 4rpx 12rpx;
	border-radius: 4px;
	position: relative;
}

.header-right.unread {
	background: rgba(255, 107, 53, 0.1);
	border: 1px solid rgba(255, 107, 53, 0.3);
}

.header-right.read {
	background: rgba(0, 255, 136, 0.1);
	border: 1px solid rgba(0, 255, 136, 0.3);
}

.status-text {
	font-size: 24rpx;
	color: #f5f5f5;
}

.corner-decorator {
	position: absolute;
	width: 6px;
	height: 6px;
	border: 1px solid currentColor;
	opacity: 0.6;
}

.tech-card-content {
	padding: 10rpx 0;
}

.title-row {
	margin-bottom: 15rpx;
}

.message-title {
	font-size: 32rpx;
	color: #f5f5f5;
	font-weight: 500;
	line-height: 1.4;
}

.content-row {
	margin-bottom: 15rpx;
}

.message-content {
	font-size: 28rpx;
	color: #b9c8dc;
	line-height: 1.5;
}

.time-info {
	border-top: 1px solid rgba(185, 200, 220, 0.1);
	padding-top: 15rpx;
	margin-top: 15rpx;
}

.time-row {
	display: flex;
	justify-content: space-between;
	margin-bottom: 8rpx;
	font-size: 24rpx;
}

.time-label {
	color: #b9c8dc;
}

.time-value {
	color: #f5f5f5;
}

.tech-loading {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 30rpx;
}

.loading-circle {
	width: 40rpx;
	height: 40rpx;
	border: 2px solid rgba(33, 148, 255, 0.1);
	border-top-color: #2194ff;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 10rpx;
}

@keyframes spin {
	to {
		transform: rotate(360deg);
	}
}

.scroll-content {
	flex: 1;
	overflow-y: auto;
}

.no-more-data {
	text-align: center;
	color: #b9c8dc;
	font-size: 28rpx;
	padding: 20rpx 0;
}

::v-deep .uni-select__input-text {
	color: #f5f5f5;
}

::v-deep .uni-select__selector {
	background: rgba(27, 34, 51, 1);
	color: rgba(255, 255, 255, 1);
	border: none;
}

::v-deep .uni-select {
	border: none;
}

::v-deep .uni-date-editor {
	color: #f5f5f5;
}
</style>