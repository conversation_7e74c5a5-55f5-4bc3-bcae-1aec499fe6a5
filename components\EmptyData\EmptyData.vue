<template>
  <view v-if="show" class="empty-data">
    <view class="empty-icon">
      <view class="icon-wrapper">
        <view class="empty-document">
          <view class="document-header">
            <view class="header-line"></view>
            <view class="header-line short"></view>
          </view>
          <view class="document-body">
            <view class="body-line"></view>
            <view class="body-line"></view>
            <view class="body-line short"></view>
          </view>
          <view class="document-corner"></view>
        </view>
        <view class="floating-elements">
          <view class="element element-1">📄</view>
          <view class="element element-2">📋</view>
          <view class="element element-3">📊</view>
        </view>
      </view>
    </view>
    <view class="empty-content">
      <view class="empty-text">{{ title }}</view>
      <view class="empty-tip">{{ description }}</view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'EmptyData',
  props: {
    // 是否显示空数据组件
    show: {
      type: Boolean,
      default: false
    },
    // 主标题
    title: {
      type: String,
      default: '暂无数据'
    },
    // 描述文字
    description: {
      type: String,
      default: '当前筛选条件下没有找到相关信息'
    }
  }
}
</script>

<style lang="scss" scoped>
.empty-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx 80rpx;
  margin: 60rpx 20rpx;
  position: relative;
  min-height: 400rpx;
}

.empty-data::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 30% 20%, rgba(33, 148, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 70% 80%, rgba(33, 148, 255, 0.03) 0%, transparent 50%),
    linear-gradient(135deg, rgba(18, 34, 46, 0.4) 0%, rgba(27, 34, 51, 0.2) 100%);
  border-radius: 16rpx;
  border: 1px solid rgba(33, 148, 255, 0.15);
  z-index: 1;
}

.empty-data::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, 
    rgba(33, 148, 255, 0.1) 0%, 
    transparent 25%, 
    transparent 75%, 
    rgba(33, 148, 255, 0.1) 100%);
  border-radius: 18rpx;
  z-index: 0;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  background: linear-gradient(135deg, rgba(33, 148, 255, 0.1), rgba(0, 212, 255, 0.05));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
  position: relative;
  z-index: 2;
  box-shadow: 
    0 0 30rpx rgba(33, 148, 255, 0.2),
    inset 0 0 20rpx rgba(33, 148, 255, 0.1);
}

.empty-icon::before {
  content: '';
  position: absolute;
  width: 180rpx;
  height: 180rpx;
  border: 2rpx solid transparent;
  border-top-color: rgba(33, 148, 255, 0.3);
  border-right-color: rgba(33, 148, 255, 0.1);
  border-radius: 50%;
  animation: rotate 3s linear infinite;
  z-index: -1;
}

.empty-icon::after {
  content: '';
  position: absolute;
  width: 200rpx;
  height: 200rpx;
  border: 1rpx solid transparent;
  border-bottom-color: rgba(0, 212, 255, 0.2);
  border-left-color: rgba(0, 212, 255, 0.1);
  border-radius: 50%;
  animation: rotate 4s linear infinite reverse;
  z-index: -2;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.icon-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 3;
}

.empty-document {
  width: 90rpx;
  height: 110rpx;
  background: rgba(33, 148, 255, 0.08);
  border: 2rpx solid rgba(33, 148, 255, 0.3);
  border-radius: 8rpx;
  position: relative;
  padding: 15rpx 12rpx;
  animation: documentFloat 3s ease-in-out infinite;
  box-shadow: 0 8rpx 25rpx rgba(33, 148, 255, 0.15);
}

@keyframes documentFloat {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-10rpx) rotate(1deg);
  }
}

.document-corner {
  position: absolute;
  top: -2rpx;
  right: -2rpx;
  width: 20rpx;
  height: 20rpx;
  background: rgba(33, 148, 255, 0.2);
  clip-path: polygon(100% 0, 0 100%, 100% 100%);
}

.document-header {
  margin-bottom: 12rpx;
}

.header-line {
  height: 3rpx;
  background: rgba(33, 148, 255, 0.4);
  border-radius: 2rpx;
  margin-bottom: 4rpx;
  animation: linePulse 2s ease-in-out infinite;
}

.header-line.short {
  width: 60%;
  animation-delay: 0.3s;
}

.document-body {
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.body-line {
  height: 2rpx;
  background: rgba(33, 148, 255, 0.25);
  border-radius: 1rpx;
  animation: linePulse 2s ease-in-out infinite;
}

.body-line:nth-child(1) {
  animation-delay: 0.6s;
}

.body-line:nth-child(2) {
  animation-delay: 0.9s;
}

.body-line.short {
  width: 70%;
  animation-delay: 1.2s;
}

@keyframes linePulse {
  0%, 100% {
    opacity: 0.3;
    transform: scaleX(1);
  }
  50% {
    opacity: 0.7;
    transform: scaleX(0.95);
  }
}

.floating-elements {
  position: absolute;
  width: 200rpx;
  height: 200rpx;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.element {
  position: absolute;
  font-size: 24rpx;
  opacity: 0.4;
  animation: float 4s ease-in-out infinite;
}

.element-1 {
  top: 10%;
  left: 20%;
  animation-delay: 0s;
}

.element-2 {
  top: 60%;
  right: 15%;
  animation-delay: 1.3s;
}

.element-3 {
  bottom: 20%;
  left: 30%;
  animation-delay: 2.6s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.2;
  }
  50% {
    transform: translateY(-15rpx) rotate(5deg);
    opacity: 0.6;
  }
}

.empty-content {
  text-align: center;
  z-index: 2;
  position: relative;
}

.empty-text {
  font-size: 34rpx;
  color: #f5f5f5;
  font-weight: 600;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  letter-spacing: 1rpx;
}

.empty-tip {
  font-size: 26rpx;
  color: #b9c8dc;
  line-height: 1.6;
  opacity: 0.9;
  max-width: 420rpx;
  margin: 0 auto;
}
</style>
