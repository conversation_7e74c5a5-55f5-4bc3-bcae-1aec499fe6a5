import request from '@/utils/request'
// 登录方法
// export function login(username, password) {
//   const data = {
// 	grant_type: "password",
// 	client_id: "iotkit",
//     username,
//     password
//   }
//   return request({
//     'url': '/oauth2/token',
//     header: {
//       isToken: false
//     },
//     'method': 'post',
//     'data': data
//   })
// }

// 账号密码登录方法
export function accountLogin(username, password) {
  return request({
    'url': '/auth/appLogin',
    'method': 'post',
    header: {
      isToken: false
    },
    'data': {
      data: {
        username: username,
        password: password
      }
    }
  })
}

// 原微信登录方法（保留作为备用）
export function login(username, password, code) {
  return request({
    'url': '/auth/xcxLogin',
    'method': 'post',
    header: {
      isToken: false
    },
    'data': {
      data: {
        "appId": "wx44559984df3ab083",
        "code": code,
		"username":username,
		"password":password,
      },
    }
  })
}

// 企业微信登录（保留作为备用）
export function entLogin(code,corpId) {
	// console.log(code,'=====');
  return request({
    'url': '/auth/entXcxLogin',
    'method': 'post',
    header: {
      isToken: false
    },
    'data': {
      data: {
        "code": code,
		"appId":corpId
      },
    }
  })
}
// 企业微信登录信息（保留作为备用）
export function entXcxMoile(mobile,tenantId,userInfo){
	return request({
    'url': '/auth/entXcx/register/moile',
    'method': 'post',
    'data': {
      data: {
        "mobile": mobile,
		"tenantId":tenantId,
		"userInfo":userInfo
      },
    }
  })
	
}
// 获取用户详细信息
export function getInfo(data) {
  return request({
    'url': '/system/user/getInfo',
    'method': 'Post',
	data: data
  })
}
// 获取项目列表
export function getProject() {
  return request({
    'url': '/project/relation/list',
    'method': 'get'
  })
}

// 退出方法
// export function logout(accessToken) {
// 	const data = {
// 		accessToken
// 	}
//   return request({
//     'url': '/oauth2/logout',
//     'method': 'get',
// 	'params':data
//   })
// }

// 获取验证码
export function getCodeImg() {
  return request({
    'url': '/captchaImage',
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  })
}
