const TokenKey = 'App-Token'
const ClientId = 'client_id'
// 选择的项目
const Project = 'project'
// 项目列表
const ProjectList = 'projectList'
// 获取用户角色
const Role = 'role'

export function getToken() {
  return uni.getStorageSync(TokenKey)
}


export function setToken(token) {
  return uni.setStorageSync(TokenKey, token)
}

export function removeToken() {
  return uni.removeStorageSync(TokenKey)
}

export function setClientId(clientId) {
  return uni.setStorageSync(ClientId,clientId)
}

// 获取缓存的选择项目
export function getProject() {
  return uni.getStorageSync(Project)
}
// 更新选择的项目
export function setProject(project) {
  return uni.setStorageSync(Project, project)
}
// 获取项目列表
export function getProjectList() {
  return uni.getStorageSync(ProjectList)
}
// 更新项目列表
export function setProjectList(projectList) {
  return uni.setStorageSync(ProjectList, projectList)
}
// 获取用户角色
export function getRole() {
  return uni.getStorageSync(Role)
}
// 更新用户角色
export function setRole(role) {
  return uni.setStorageSync(Role, role)
}
// 清除缓存
export function clearStorage() {
  uni.clearStorageSync()
}

