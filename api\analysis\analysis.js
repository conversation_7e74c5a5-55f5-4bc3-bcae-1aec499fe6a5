import request from '@/utils/request'

// 获取智能诊断
export function getIntelligentDiagnosis(params) {
  return request({
    url: '/manager/analysisReport/intelligent/analysis',
    method: 'Post',
   data: params
  })
}

// 获取关键指标分析
export function getIndicatorAnalysis(params) {
  return request({
    url: '/manager/analysisReport/main/point/analysis',
    method: 'Post',
    data: params
  })
}

export function homeOperations(params) {
  return request({
    url: '/device/dashboard/intelligent/operation',
    method: 'Post',
    data: params
  })
}