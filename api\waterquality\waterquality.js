import request from '@/utils/request'

// 查询水质报告标签
export function waterqualityConfiglable(data) {
  return request({
    url: '/project/powerPointConfig/all',
    method: 'Post',
	data: data
  })
}

// 获取水质报告数据
export function waterqualityConfigData(data) {
  return request({
    url: '/project/powerPointConfig/point/data',
    method: 'Post',
	data: data
  })
}

// 点击点位获取水质报告图表数据
export function waterqualityConfigEcharts(data) {
  return request({
    url: '/device/deviceProperty/downSampling/list',
    method: 'Post',
	data: data
  })
}
// 提交水质报告

export function waterqualityAdd(data) {
  return request({
    url: '/project/powerPointConfig/device/data/add',
    method: 'Post',
	data: data
  })
}