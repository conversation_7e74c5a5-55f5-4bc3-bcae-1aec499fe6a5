<!-- ULoadingPage.vue -->
<template>
  <view v-if="visible" class="loading-overlay">
    <u-loading-page
      bg-color="#e8e8e8"
      font-size="24"
      color="#666"
      icon-size="36"
      loading-text="loading..."
    ></u-loading-page>
  </view>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  }
};
</script>

<style scoped>
/* .loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(232, 232, 232, 0.8);
  z-index: 9999;
} */
</style>
