<template>
	<view class="container">
		<!-- 简化的头部 -->
		<AppHeader flex title="[CIMS]指标看板" />
		
		<!-- 简化的时间选择器 -->
		<view class="time-selector-panel">
			<view class="time-selector" @click="openCalendar">
				<text class="selector-text">{{ currentDateRange }}</text>
				<text class="calendar-icon">📅</text>
			</view>
		</view>
		
		<!-- 时间范围快速选择 -->
		<view v-if="calendarShow" class="time-range-modal">
			<view class="modal-content">
				<view class="modal-header">
					<text class="modal-title">选择时间范围</text>
					<text class="modal-close" @click="calendarClose">✕</text>
				</view>
				
				<!-- 快速选择按钮 -->
				<view class="quick-select">
					<text class="quick-title">快速选择:</text>
					<view class="quick-buttons">
						<text class="quick-btn" @click="selectTimeRange('today')">今天</text>
						<text class="quick-btn" @click="selectTimeRange('yesterday')">昨天</text>
						<text class="quick-btn" @click="selectTimeRange('last7days')">最近7天</text>
						<text class="quick-btn" @click="selectTimeRange('last30days')">最近30天</text>
					</view>
				</view>
				
				<!-- 自定义时间选择 -->
				<view class="custom-select">
					<text class="custom-title">自定义时间:</text>
					<view class="date-inputs">
						<view class="date-input-group">
							<text class="input-label">开始日期:</text>
							<view class="date-input" @click="openStartDatePicker">
								<text class="date-text">{{ startDateDisplay }}</text>
								<text class="date-icon">📅</text>
							</view>
						</view>
						<view class="date-input-group">
							<text class="input-label">结束日期:</text>
							<view class="date-input" @click="openEndDatePicker">
								<text class="date-text">{{ endDateDisplay }}</text>
								<text class="date-icon">📅</text>
							</view>
						</view>
					</view>
					<view class="action-buttons">
						<text class="action-btn confirm-btn" @click="confirmCustomRange">确认</text>
						<text class="action-btn cancel-btn" @click="calendarClose">取消</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 单个日期选择器 -->
		<u-picker 
			:show="startDatePickerShow" 
			:columns="startDateColumns" 
			@confirm="confirmStartDate" 
			@cancel="startDatePickerShow = false"
			:mask-style="{ opacity: 0 }"
		></u-picker>
		
		<u-picker 
			:show="endDatePickerShow" 
			:columns="endDateColumns" 
			@confirm="confirmEndDate" 
			@cancel="endDatePickerShow = false"
			:mask-style="{ opacity: 0 }"
		></u-picker>
		
		<!-- 图表区域 -->
		<scroll-view class="scroll-content" scroll-y="true">
			<view v-if="chartArr.length > 0" class="charts-container">
				<view v-for="(item, index) in chartArr" :key="index" class="chart-item">
					<view class="chart-header">
						<view class="chart-title">{{ item.name }}</view>
						<view class="chart-unit">{{ item.unit || '无单位' }}</view>
					</view>
					
					<!-- 图表区域 -->
					<view class="chart-wrapper">
						<SimpleChart
							v-if="chartDataList[index]"
							:chart-data="chartDataList[index]"
							:key="'chart-' + index + '-' + updateTimestamp"
						/>
						<view v-else-if="chartDataList[index] === null" class="chart-error">
							<text>❌ {{ item.name }} - 数据加载失败</text>
							<text class="error-detail">请检查控制台日志了解详细错误信息</text>
						</view>
						<view v-else class="chart-loading">
							<text>⏳ {{ item.name }} - 正在加载...</text>
						</view>
					</view>
					
					<!-- 统计信息 -->
					<view v-if="chartDataList[index] && chartDataList[index].stats" class="chart-stats">
						<view class="stats-item">
							<text class="stats-label">平均值:</text>
							<text class="stats-value">{{ chartDataList[index].stats.avg }}{{ item.unit }}</text>
						</view>
						<view class="stats-item">
							<text class="stats-label">最大值:</text>
							<text class="stats-value">{{ chartDataList[index].stats.max }}{{ item.unit }}</text>
						</view>
						<view class="stats-item">
							<text class="stats-label">最小值:</text>
							<text class="stats-value">{{ chartDataList[index].stats.min }}{{ item.unit }}</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 空数据组件 -->
			<EmptyData
				:show="chartArr.length === 0"
				title="暂无图表数据"
				description="请配置相关指标或检查数据源连接"
			/>
		</scroll-view>
	</view>
</template>

<script>
	import { powerPointConfigEcharts } from '@/api/kanban/kanban.js';
	import SimpleChart from '@/components/echarts/SimpleChart.vue';
	import EmptyData from '@/components/EmptyData/EmptyData.vue';

	export default {
		components: {
			SimpleChart,
			EmptyData
		},
		data() {
			return {
				chartArr: [],
				chartDataList: [], // 存储每个图表的数据
				calendarShow: false,
				currentDateRange: '请选择日期',
				selectedTimeRange: {
					startTime: '',
					endTime: ''
				},
				// 日期选择器相关
				startDatePickerShow: false,
				endDatePickerShow: false,
				startDateDisplay: '请选择开始日期',
				endDateDisplay: '请选择结束日期',
				tempStartDate: '',
				tempEndDate: '',
				startDateColumns: [],
				endDateColumns: [],
				// 强制更新时间戳
				updateTimestamp: Date.now()
			};
		},

		methods: {
			// 打开日历
			openCalendar() {
				this.calendarShow = true;
				this.initDatePickers();
			},

			// 关闭日历
			calendarClose() {
				this.calendarShow = false;
				this.startDatePickerShow = false;
				this.endDatePickerShow = false;
			},
			
			// 初始化日期选择器数据
			initDatePickers() {
				const today = new Date();
				const currentYear = today.getFullYear();

				// 生成年份选项（过去2年到今年）
				const years = [];
				for (let year = currentYear - 2; year <= currentYear; year++) {
					years.push(year.toString());
				}

				// 生成月份选项
				const months = [];
				for (let month = 1; month <= 12; month++) {
					months.push(month.toString().padStart(2, '0'));
				}

				// 生成日期选项
				const days = [];
				for (let day = 1; day <= 31; day++) {
					days.push(day.toString().padStart(2, '0'));
				}

				this.startDateColumns = [years, months, days];
				this.endDateColumns = [years, months, days];
			},
			
			// 快速选择时间范围
			selectTimeRange(type) {
				const now = new Date();
				let startTime, endTime, displayText;

				switch (type) {
					case 'today':
						const today = this.formatDate(now);
						startTime = `${today} 00:00:00`;
						endTime = `${today} 23:59:59`;
						displayText = '今天';
						break;
					case 'yesterday':
						const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
						const yesterdayStr = this.formatDate(yesterday);
						startTime = `${yesterdayStr} 00:00:00`;
						endTime = `${yesterdayStr} 23:59:59`;
						displayText = '昨天';
						break;
					case 'last7days':
						const last7days = new Date(now.getTime() - 6 * 24 * 60 * 60 * 1000);
						startTime = `${this.formatDate(last7days)} 00:00:00`;
						endTime = `${this.formatDate(now)} 23:59:59`;
						displayText = '最近7天';
						break;
					case 'last30days':
						const last30days = new Date(now.getTime() - 29 * 24 * 60 * 60 * 1000);
						startTime = `${this.formatDate(last30days)} 00:00:00`;
						endTime = `${this.formatDate(now)} 23:59:59`;
						displayText = '最近30天';
						break;
				}

				this.selectedTimeRange = { startTime, endTime };
				this.currentDateRange = displayText;
				this.updateTimestamp = Date.now(); // 更新时间戳，强制图表重新渲染
				this.calendarClose();
				this.loadAllChartData();
			},
			
			// 打开开始日期选择器
			openStartDatePicker() {
				this.startDatePickerShow = true;
			},

			// 打开结束日期选择器
			openEndDatePicker() {
				this.endDatePickerShow = true;
			},

			// 确认开始日期
			confirmStartDate(e) {
				const values = e.value;
				const dateStr = `${values[0]}-${values[1]}-${values[2]}`;
				this.tempStartDate = dateStr;
				this.startDateDisplay = dateStr;
				this.startDatePickerShow = false;
			},

			// 确认结束日期
			confirmEndDate(e) {
				const values = e.value;
				const dateStr = `${values[0]}-${values[1]}-${values[2]}`;
				this.tempEndDate = dateStr;
				this.endDateDisplay = dateStr;
				this.endDatePickerShow = false;
			},
			
			// 确认自定义时间范围
			confirmCustomRange() {
				if (!this.tempStartDate || !this.tempEndDate) {
					uni.showToast({
						title: '请选择完整的时间范围',
						icon: 'none'
					});
					return;
				}

				// 验证时间范围合理性
				const startDate = new Date(this.tempStartDate);
				const endDate = new Date(this.tempEndDate);

				if (startDate > endDate) {
					uni.showToast({
						title: '开始日期不能晚于结束日期',
						icon: 'none'
					});
					return;
				}

				this.selectedTimeRange = {
					startTime: `${this.tempStartDate} 00:00:00`,
					endTime: `${this.tempEndDate} 23:59:59`
				};

				if (this.tempStartDate === this.tempEndDate) {
					this.currentDateRange = this.tempStartDate;
				} else {
					this.currentDateRange = `${this.tempStartDate} ~ ${this.tempEndDate}`;
				}

				this.updateTimestamp = Date.now(); // 更新时间戳，强制图表重新渲染

				this.calendarClose();
				this.loadAllChartData();
			},
			
			// 日历确认选择
			calendarConfirm(dates) {
				this.calendarShow = false;
				
				if (dates.length > 0) {
					const startDate = dates[0];
					const endDate = dates[dates.length - 1];
					
					// 格式化时间范围
					this.selectedTimeRange = {
						startTime: `${startDate} 00:00:00`,
						endTime: `${endDate} 23:59:59`
					};
					
					// 更新显示文本
					if (startDate === endDate) {
						this.currentDateRange = startDate;
					} else {
						this.currentDateRange = `${startDate} ~ ${endDate}`;
					}
					
					// 重新加载图表数据
					this.loadAllChartData();
				}
			},
			
			// 加载所有图表数据
			loadAllChartData() {
				if (!this.chartArr || this.chartArr.length === 0) {
					return;
				}

				if (!this.selectedTimeRange.startTime || !this.selectedTimeRange.endTime) {
					return;
				}

				// 初始化chartDataList数组，确保有正确的长度
				this.chartDataList = new Array(this.chartArr.length).fill(undefined);

				// 为每个指标加载数据
				this.chartArr.forEach((item, index) => {
					this.loadChartData(item, index);
				});
			},
			
			// 加载单个图表数据
			loadChartData(chartItem, index) {
				// 验证必要字段
				if (!chartItem.deviceId || !chartItem.identifier) {
					this.$set(this.chartDataList, index, null);
					return;
				}

				const params = {
					data: {
						name: chartItem.name,
						unit: chartItem.unit,
						deviceId: chartItem.deviceId,
						identifier: chartItem.identifier,
						startTime: this.selectedTimeRange.startTime,
						endTime: this.selectedTimeRange.endTime,
						displayStats: true,
						displayGrowth: true,
						clientType: "wechat"
					}
				};

				powerPointConfigEcharts(params).then(res => {
					if (res.code === 200 && res.data) {
						const data = res.data;

						// 处理图表数据
						const chartData = this.processChartData(data);

						// 使用 $set 确保响应式更新
						this.$set(this.chartDataList, index, chartData);
					} else {
						this.$set(this.chartDataList, index, null);
					}
				}).catch(() => {
					this.$set(this.chartDataList, index, null);
				});
			},

			// 判断时间范围是否超过一天
			isTimeRangeMultipleDays() {
				if (!this.selectedTimeRange.startTime || !this.selectedTimeRange.endTime) {
					return false;
				}

				const startDate = new Date(this.selectedTimeRange.startTime);
				const endDate = new Date(this.selectedTimeRange.endTime);

				// 计算时间差（毫秒）
				const timeDiff = endDate.getTime() - startDate.getTime();
				// 转换为天数
				const daysDiff = timeDiff / (1000 * 60 * 60 * 24);

				// 如果超过1天，返回true
				return daysDiff > 1;
			},

			// 处理图表数据
			processChartData(apiData) {
				// 提取时间和数值数据
				const xdata = [];
				const valueData = [];
				const tbValueData = [];
				const hbValueData = [];

				// 判断时间范围是否超过一天
				const isMultipleDays = this.isTimeRangeMultipleDays();

				if (apiData.his && apiData.his.length > 0) {
					apiData.his.forEach(item => {
						if (item.time) {
							const date = new Date(item.time);
							let timeStr;

							if (isMultipleDays) {
								// 超过一天：显示月-日格式
								const month = String(date.getMonth() + 1).padStart(2, '0');
								const day = String(date.getDate()).padStart(2, '0');
								timeStr = `${month}-${day}`;
							} else {
								// 一天内：显示小时:分钟格式
								const hours = String(date.getHours()).padStart(2, '0');
								const minutes = String(date.getMinutes()).padStart(2, '0');
								timeStr = `${hours}:${minutes}`;
							}

							xdata.push(timeStr);

							// 处理主要数值：当值为null或undefined时保持null，让图表显示断点效果
							if (item.value === null || item.value === undefined) {
								valueData.push(null);
							} else {
								valueData.push(item.value);
							}

							// 处理同比数值
							if (item.tbValue === null || item.tbValue === undefined) {
								tbValueData.push(null);
							} else {
								tbValueData.push(item.tbValue);
							}

							// 处理环比数值
							if (item.hbValue === null || item.hbValue === undefined) {
								hbValueData.push(null);
							} else {
								hbValueData.push(item.hbValue);
							}
						}
					});
				}

				// 构造图表组件需要的数据格式
				const series = [];

				// 主要数据系列
				series.push({
					name: apiData.name || '实际值',
					data: valueData,
					connectNulls: false  // 确保null值处线条断开
				});

				// 同比数据系列（如果有数据）
				const hasTbData = tbValueData.some(val => val !== null);
				if (hasTbData) {
					series.push({
						name: '同比',
						data: tbValueData,
						connectNulls: false
					});
				}

				// 环比数据系列（如果有数据）
				const hasHbData = hbValueData.some(val => val !== null);
				if (hasHbData) {
					series.push({
						name: '环比',
						data: hbValueData,
						connectNulls: false
					});
				}

				const chartData = {
					xdata: xdata,
					series: series,
					title: apiData.name || '图表数据',
					unit: apiData.unit || '',
					showLegend: series.length > 1, // 多个系列时显示图例
					stats: {
						avg: apiData.avg,
						max: apiData.max,
						min: apiData.min
					}
				};

				return chartData;
			},
			
			// 获取图表标题
			getChartTitle(data) {
				const parts = [];
				if (data.avg !== null && data.avg !== undefined) {
					parts.push(`平均值: ${data.avg}`);
				}
				if (data.max !== null && data.max !== undefined) {
					parts.push(`最大值: ${data.max}`);
				}
				if (data.min !== null && data.min !== undefined) {
					parts.push(`最小值: ${data.min}`);
				}
				
				return parts.length > 0 ? parts.join(' | ') : '暂无统计数据';
			},
			
			// 初始化日历设置
			initCalendar() {
				const now = new Date();
				const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
				
				this.calendar.defaultDate = today.getTime();
				this.calendar.maxDate = today.getTime();
				
				// 默认选择今天
				const todayStr = this.formatDate(today);
				this.selectedTimeRange = {
					startTime: `${todayStr} 00:00:00`,
					endTime: `${todayStr} 23:59:59`
				};
				this.currentDateRange = todayStr;
			},
			
			// 格式化日期
			formatDate(date) {
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				return `${year}-${month}-${day}`;
			}
		},
		mounted() {
			this.initCalendar();
		},
		onLoad: function(option) {
			if (option.data) {
				try {
					// 尝试多种解码方式
					let dataString;
					try {
						// 首先尝试uni.base64解码
						if (uni.base64 && uni.base64.decode) {
							dataString = uni.base64.decode(option.data);
						} else {
							// 尝试浏览器原生atob
							dataString = decodeURIComponent(atob(option.data));
						}
					} catch (base64Error) {
						try {
							// 如果Base64解码失败，尝试URL解码
							dataString = decodeURIComponent(option.data);
						} catch (urlError) {
							throw new Error('数据解码失败');
						}
					}

					const rawData = JSON.parse(dataString);

					// 检查数据是否是数组格式
					if (Array.isArray(rawData)) {
						// 如果是数组，直接使用
						this.chartArr = rawData;
					} else {
						// 如果不是数组，包装成数组
						this.chartArr = [rawData];
					}

					// 页面加载完成后默认选择今天并自动加载数据
					setTimeout(() => {
						this.selectTimeRange('today');
					}, 200);
				} catch (error) {
					this.chartArr = [];

					// 显示错误提示
					uni.showToast({
						title: '数据解析失败',
						icon: 'none',
						duration: 2000
					});
				}
			} else {
				this.chartArr = [];
			}
		}
	};
</script>

<style scoped>
	.container {
		height: 100vh;
		background: linear-gradient(135deg, #0a0f1c 0%, #1a1f2e 50%, #0f1419 100%);
		overflow: hidden;
	}

	/* 简化的头部样式 */
	.simple-header {
		height: 88rpx;
		background: rgba(33, 148, 255, 0.15);
		display: flex;
		align-items: center;
		justify-content: center;
		border-bottom: 1px solid rgba(33, 148, 255, 0.3);
	}

	.header-title {
		color: #fff;
		font-size: 32rpx;
		font-weight: bold;
	}

	.calendar-icon {
		font-size: 32rpx;
		margin-left: 16rpx;
	}

	/* 时间范围选择器样式 */
	.time-range-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.8);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
	}

	.modal-content {
		background: linear-gradient(135deg, #1a1f2e 0%, #2a2f3e 100%);
		border: 1px solid rgba(33, 148, 255, 0.3);
		border-radius: 20rpx;
		width: 640rpx;
		max-height: 80vh;
		overflow-y: auto;
		box-shadow: 0 0 30rpx rgba(33, 148, 255, 0.3);
	}

	.modal-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx 40rpx;
		border-bottom: 1px solid rgba(33, 148, 255, 0.3);
		background: rgba(33, 148, 255, 0.1);
	}

	.modal-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #fff;
		text-shadow: 0 0 8rpx rgba(33, 148, 255, 0.5);
	}

	.modal-close {
		font-size: 48rpx;
		color: rgba(255, 255, 255, 0.7);
		font-weight: bold;
		padding: 10rpx;
		line-height: 1;
	}

	/* 快速选择区域 */
	.quick-select {
		padding: 30rpx 40rpx;
		border-bottom: 1px solid rgba(33, 148, 255, 0.2);
	}

	.quick-title {
		font-size: 28rpx;
		color: #fff;
		margin-bottom: 20rpx;
		display: block;
	}

	.quick-buttons {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
	}

	.quick-btn {
		background: linear-gradient(135deg, rgba(33, 148, 255, 0.2) 0%, rgba(0, 255, 136, 0.1) 100%);
		border: 1px solid rgba(33, 148, 255, 0.4);
		color: #fff;
		padding: 20rpx 30rpx;
		border-radius: 12rpx;
		font-size: 26rpx;
		min-width: 120rpx;
		text-align: center;
		transition: all 0.3s ease;
	}

	.quick-btn:active {
		background: linear-gradient(135deg, rgba(33, 148, 255, 0.4) 0%, rgba(0, 255, 136, 0.2) 100%);
		transform: scale(0.95);
	}

	/* 自定义选择区域 */
	.custom-select {
		padding: 30rpx 40rpx;
	}

	.custom-title {
		font-size: 28rpx;
		color: #fff;
		margin-bottom: 20rpx;
		display: block;
	}

	.date-inputs {
		margin-bottom: 30rpx;
	}

	.date-input-group {
		margin-bottom: 20rpx;
	}

	.input-label {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.7);
		margin-bottom: 10rpx;
		display: block;
	}

	.date-input {
		display: flex;
		align-items: center;
		justify-content: space-between;
		background: rgba(33, 148, 255, 0.1);
		border: 1px solid rgba(33, 148, 255, 0.3);
		border-radius: 12rpx;
		padding: 20rpx 24rpx;
		min-height: 80rpx;
	}

	.date-text {
		font-size: 28rpx;
		color: #fff;
		flex: 1;
	}

	.date-icon {
		font-size: 32rpx;
		color: rgba(255, 255, 255, 0.7);
	}

	.action-buttons {
		display: flex;
		gap: 20rpx;
		justify-content: flex-end;
	}

	.action-btn {
		padding: 24rpx 40rpx;
		border-radius: 12rpx;
		font-size: 28rpx;
		text-align: center;
		min-width: 120rpx;
		font-weight: bold;
	}

	.confirm-btn {
		background: linear-gradient(135deg, #2194FF 0%, #00ff88 100%);
		color: #fff;
		box-shadow: 0 0 20rpx rgba(33, 148, 255, 0.5);
	}

	.cancel-btn {
		background: rgba(255, 255, 255, 0.1);
		border: 1px solid rgba(255, 255, 255, 0.3);
		color: #fff;
	}

	/* 时间选择器样式 */
	.time-selector-panel {
		padding: 20rpx;
		background: rgba(33, 148, 255, 0.1);
		border-bottom: 1px solid rgba(33, 148, 255, 0.3);
	}

	.time-selector {
		display: flex;
		align-items: center;
		justify-content: space-between;
		background: rgba(33, 148, 255, 0.15);
		border: 1px solid rgba(33, 148, 255, 0.4);
		border-radius: 12rpx;
		padding: 20rpx 24rpx;
		min-height: 80rpx;
	}

	.selector-text {
		font-size: 28rpx;
		font-weight: 500;
		color: #fff;
	}

	/* 滚动内容区域 */
	.scroll-content {
		height: calc(100vh - 160rpx);
		overflow-y: auto;
		padding-bottom: 40rpx;
	}

	/* 图表容器 */
	.charts-container {
		padding: 20rpx;
		padding-bottom: 60rpx;
	}

	.chart-item {
		background: linear-gradient(135deg, rgba(33, 148, 255, 0.1) 0%, rgba(0, 255, 136, 0.05) 100%);
		border: 1px solid rgba(33, 148, 255, 0.3);
		border-radius: 16rpx;
		margin-bottom: 30rpx;
		overflow: hidden;
	}

	.chart-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx 28rpx;
		background: rgba(33, 148, 255, 0.15);
		border-bottom: 1px solid rgba(33, 148, 255, 0.3);
	}

	.chart-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #fff;
		text-shadow: 0 0 8rpx rgba(33, 148, 255, 0.5);
	}

	.chart-unit {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.7);
		background: rgba(0, 255, 136, 0.2);
		padding: 6rpx 12rpx;
		border-radius: 20rpx;
		border: 1px solid rgba(0, 255, 136, 0.3);
	}

	.chart-wrapper {
		height: 400rpx;
		position: relative;
		background: rgba(0, 0, 0, 0.2);
	}

	.chart-loading {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 100%;
		color: rgba(255, 255, 255, 0.6);
		font-size: 28rpx;
	}

	/* 图表错误状态显示 */
	.chart-error {
		padding: 30rpx;
		color: #fff;
		display: flex;
		flex-direction: column;
		gap: 15rpx;
		text-align: center;
		align-items: center;
		justify-content: center;
		height: 100%;
	}

	.chart-error text:first-child {
		font-size: 28rpx;
		color: #ff6b35;
		font-weight: bold;
	}

	.error-detail {
		font-size: 22rpx;
		color: rgba(255, 255, 255, 0.6);
	}

	/* 统计信息 */
	.chart-stats {
		display: flex;
		justify-content: space-around;
		padding: 20rpx 28rpx;
		background: rgba(0, 0, 0, 0.3);
		border-top: 1px solid rgba(33, 148, 255, 0.3);
	}

	.stats-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 8rpx;
	}

	.stats-label {
		font-size: 22rpx;
		color: rgba(255, 255, 255, 0.6);
	}

	.stats-value {
		font-size: 26rpx;
		font-weight: bold;
		color: #00ff88;
		font-family: 'Courier New', monospace;
	}

	/* 无数据提示 */
	.no-data {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 400rpx;
		color: rgba(255, 255, 255, 0.5);
		font-size: 32rpx;
	}
</style>