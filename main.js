import Vue from 'vue'
import App from './App'
import store from './store' // store
import plugins from './plugins' // plugins
// 引入全局uView
import uView from '@/uni_modules/uview-ui'
import './permission' // permission
import Header from "@/components/header/header.vue";

Vue.component('AppHeader', Header)

Vue.use(plugins)
Vue.use(uView)

Vue.config.productionTip = false
Vue.prototype.$store = store

App.mpType = 'app'

const app = new Vue({
  ...App
})

app.$mount()
