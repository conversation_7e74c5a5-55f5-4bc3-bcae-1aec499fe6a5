import upload from '@/utils/upload'
import request from '@/utils/request'

// 用户密码重置
export function updateUserPwd(uid, oldPassword, newPassword) {
  return request({
    url: `/app/user/${uid}/modifyPwd`,
    method: 'Post',
    params: {
      newPwd: newPassword,
      oldPwd: oldPassword
    }
  })
}

// 查询用户个人信息
export function getUserProfile() {
  return request({
    url: '/system/user/getInfo',
    method: 'Post'
  })
}

// 修改用户个人信息
export function updateUserProfile(data) {
  return request({
    url: '/app/user/client/user/save',
    method: 'Post',
    data
  })
}

// 用户头像上传
export function uploadAvatar(data) {
  return upload({
    url: '/system/user/profile/avatar',
    name: data.name,
    filePath: data.filePath
  })
}

// 查询OSS对象基于id串
export function listByIds(data) {
  return request({
    url: '/resource/oss/listByIds',
    method: 'Post',
    data
  })
}

