import store from '@/store'
import config from '@/config'
import { getToken } from '@/utils/auth'
import errorCode from '@/utils/errorCode'
import { toast, showConfirm, tansParams } from '@/utils/common'
import { generateUUID } from '@/utils/util'
import storage from '@/utils/storage'

let timeout = 10000
const baseUrl = config.baseUrl

// 添加一个全局标志，防止重复处理401
let isHandling401 = false

const request = config => {
  // 是否需要设置 token
  const isToken = (config.header || {}).isToken === false
  config.header = config.header || {}
  if (getToken() && !isToken) {
    config.header['token'] = getToken()
  }

  // get请求映射params参数
  if (config.params) {
    let url = config.url + '?' + tansParams(config.params)
    url = url.slice(0, -1)
    config.url = url
  }
  config.data = {
    requestId: generateUUID(),
    ...config.data
  }

  return new Promise((resolve, reject) => {
    uni.request({
      method: config.method || 'get',
      timeout: config.timeout || timeout,
      url: config.baseUrl || baseUrl + config.url,
      data: config.data,
      header: config.header,
      dataType: 'json'
    }).then(response => {
      let res = response[1]
      let statusCode = res.data.code

      if (statusCode === 401) {
        if (!isHandling401) {
          isHandling401 = true
          storage.clean()
          store.dispatch('LogOut').then(() => {
            uni.reLaunch({ url: '/pages/login/login' })
            isHandling401 = false
          })
        }
        reject('未授权，请重新登录')
        return
      }

      if (statusCode !== 200) {
        // 对于非401错误，仅显示toast
        toast(res.data.message || '请求失败')
        reject(res.data.message || '请求失败')
        return
      }

      if (res.data.message && res.data.message.length !== 0) {
        uni.showModal({
          title: '提示',
          content: `${res.data.message}`,
          showCancel: false
        })
      }

      resolve(res.data)
    }).catch(error => {
      let { message } = error
      if (message === 'Network Error') {
        message = '后端接口连接异常'
      } else if (message.includes('timeout')) {
        message = '系统接口请求超时'
      } else if (message.includes('Request failed with status code')) {
        message = '系统接口' + message.substr(message.length - 3) + '异常'
      }
      toast(message)
      reject(message)
    })
  })
}

export default request
