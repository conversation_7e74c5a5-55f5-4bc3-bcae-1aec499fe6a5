<template>
	<view class="container">
		<AppHeader flex :title="chartTitle" />
		
		<view class="time-selector-panel">
			<view class="time-selector" @click="openCalendar">
				<text class="selector-text">{{ currentDateRange }}</text>
				<text class="calendar-icon">📅</text>
			</view>
		</view>
		
		<view v-if="calendarShow" class="time-range-modal">
			<view class="modal-content">
				<view class="modal-header">
					<text class="modal-title">选择时间范围</text>
					<text class="modal-close" @click="calendarClose">✕</text>
				</view>
				
				<view class="quick-select">
					<text class="quick-title">快速选择:</text>
					<view class="quick-buttons">
						<text class="quick-btn" @click="selectTimeRange('today')">今天</text>
						<text class="quick-btn" @click="selectTimeRange('yesterday')">昨天</text>
						<text class="quick-btn" @click="selectTimeRange('last7days')">最近7天</text>
						<text class="quick-btn" @click="selectTimeRange('last30days')">最近30天</text>
					</view>
				</view>
				
				<view class="custom-select">
					<text class="custom-title">自定义时间:</text>
					<view class="date-inputs">
						<view class="date-input-group">
							<text class="input-label">开始日期:</text>
							<view class="date-input" @click="openStartDatePicker">
								<text class="date-text">{{ startDateDisplay }}</text>
								<text class="date-icon">📅</text>
							</view>
						</view>
						<view class="date-input-group">
							<text class="input-label">结束日期:</text>
							<view class="date-input" @click="openEndDatePicker">
								<text class="date-text">{{ endDateDisplay }}</text>
								<text class="date-icon">📅</text>
							</view>
						</view>
					</view>
					<view class="action-buttons">
						<text class="action-btn confirm-btn" @click="confirmCustomRange">确认</text>
						<text class="action-btn cancel-btn" @click="calendarClose">取消</text>
					</view>
				</view>
			</view>
		</view>
		
		<u-picker 
			:show="startDatePickerShow" 
			:columns="startDateColumns" 
			@confirm="confirmStartDate" 
			@cancel="startDatePickerShow = false"
			:mask-style="{ opacity: 0 }"
		></u-picker>
		
		<u-picker 
			:show="endDatePickerShow" 
			:columns="endDateColumns" 
			@confirm="confirmEndDate" 
			@cancel="endDatePickerShow = false"
			:mask-style="{ opacity: 0 }"
		></u-picker>
		
		<scroll-view class="scroll-content" scroll-y="true">
			<view class="chart-container">
				<view class="chart-item">
					<view class="chart-header">
						<view class="chart-title">{{ chartData.name }}</view>
						<view class="chart-unit">{{ chartData.unit || '无单位' }}</view>
					</view>
					
					<view class="chart-wrapper">
						<SimpleChart 
							v-if="processedChartData" 
							:chart-data="{
								...processedChartData,
								showLegend: true
							}"
							:key="'chart'"
						/>
						<view v-else-if="chartError" class="chart-error">
							<text>❌ {{ chartData.name }} - 数据加载失败</text>
							<text class="error-detail">请检查控制台日志了解详细错误信息</text>
						</view>
						<view v-else class="chart-loading">
							<text>⏳ {{ chartData.name }} - 正在加载...</text>
						</view>
					</view>
					
					<view v-if="processedChartData && processedChartData.stats" class="chart-stats">
						<view class="stats-item">
							<text class="stats-label">平均值:</text>
							<text class="stats-value">{{ processedChartData.stats.avg }}{{ chartData.unit }}</text>
						</view>
						<view class="stats-item">
							<text class="stats-label">最大值:</text>
							<text class="stats-value">{{ processedChartData.stats.max }}{{ chartData.unit }}</text>
						</view>
						<view class="stats-item">
							<text class="stats-label">最小值:</text>
							<text class="stats-value">{{ processedChartData.stats.min }}{{ chartData.unit }}</text>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import { waterqualityConfigEcharts } from '@/api/waterquality/waterquality.js';
	import SimpleChart from '@/components/echarts/SimpleChart.vue';

	export default {
		components: {
			SimpleChart
		},
		data() {
			return {
				chartData: {},
				processedChartData: null,
				chartError: false,
				chartTitle: '[CIMS]水质数据趋势',
				
				calendarShow: false,
				currentDateRange: '请选择日期',
				selectedTimeRange: {
					startTime: '',
					endTime: ''
				},
				
				startDatePickerShow: false,
				endDatePickerShow: false,
				startDateDisplay: '请选择开始日期',
				endDateDisplay: '请选择结束日期',
				tempStartDate: '',
				tempEndDate: '',
				startDateColumns: [],
				endDateColumns: []
			};
		},
		methods: {
			openCalendar() {
				this.calendarShow = true;
				this.initDatePickers();
			},
			
			calendarClose() {
				this.calendarShow = false;
				this.startDatePickerShow = false;
				this.endDatePickerShow = false;
			},
			
			initDatePickers() {
				const today = new Date();
				const currentYear = today.getFullYear();
				const currentMonth = today.getMonth() + 1;
				const currentDay = today.getDate();
				
				const years = [];
				for (let year = currentYear - 2; year <= currentYear; year++) {
					years.push(year.toString());
				}
				
				const months = [];
				for (let month = 1; month <= 12; month++) {
					months.push(month.toString().padStart(2, '0'));
				}
				
				const days = [];
				for (let day = 1; day <= 31; day++) {
					days.push(day.toString().padStart(2, '0'));
				}
				
				this.startDateColumns = [years, months, days];
				this.endDateColumns = [years, months, days];
			},
			
			selectTimeRange(type) {
				const now = new Date();
				let startTime, endTime, displayText;
				
				switch (type) {
					case 'today':
						const today = this.formatDate(now);
						startTime = `${today} 00:00:00`;
						endTime = `${today} 23:59:59`;
						displayText = '今天';
						break;
					case 'yesterday':
						const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
						const yesterdayStr = this.formatDate(yesterday);
						startTime = `${yesterdayStr} 00:00:00`;
						endTime = `${yesterdayStr} 23:59:59`;
						displayText = '昨天';
						break;
					case 'last7days':
						const last7days = new Date(now.getTime() - 6 * 24 * 60 * 60 * 1000);
						startTime = `${this.formatDate(last7days)} 00:00:00`;
						endTime = `${this.formatDate(now)} 23:59:59`;
						displayText = '最近7天';
						break;
					case 'last30days':
						const last30days = new Date(now.getTime() - 29 * 24 * 60 * 60 * 1000);
						startTime = `${this.formatDate(last30days)} 00:00:00`;
						endTime = `${this.formatDate(now)} 23:59:59`;
						displayText = '最近30天';
						break;
				}
				
				this.selectedTimeRange = { startTime, endTime };
				this.currentDateRange = displayText;
				this.calendarClose();
				this.loadChartData();
			},
			
			openStartDatePicker() {
				this.startDatePickerShow = true;
			},
			
			openEndDatePicker() {
				this.endDatePickerShow = true;
			},
			
			confirmStartDate(e) {
				const values = e.value;
				const dateStr = `${values[0]}-${values[1]}-${values[2]}`;
				this.tempStartDate = dateStr;
				this.startDateDisplay = dateStr;
				this.startDatePickerShow = false;
			},
			
			confirmEndDate(e) {
				const values = e.value;
				const dateStr = `${values[0]}-${values[1]}-${values[2]}`;
				this.tempEndDate = dateStr;
				this.endDateDisplay = dateStr;
				this.endDatePickerShow = false;
			},
			
			confirmCustomRange() {
				if (!this.tempStartDate || !this.tempEndDate) {
					uni.showToast({
						title: '请选择完整的时间范围',
						icon: 'none'
					});
					return;
				}
				
				const startDate = new Date(this.tempStartDate);
				const endDate = new Date(this.tempEndDate);
				
				if (startDate > endDate) {
					uni.showToast({
						title: '开始日期不能晚于结束日期',
						icon: 'none'
					});
					return;
				}
				
				this.selectedTimeRange = {
					startTime: `${this.tempStartDate} 00:00:00`,
					endTime: `${this.tempEndDate} 23:59:59`
				};
				
				if (this.tempStartDate === this.tempEndDate) {
					this.currentDateRange = this.tempStartDate;
				} else {
					this.currentDateRange = `${this.tempStartDate} ~ ${this.tempEndDate}`;
				}
				
				this.calendarClose();
				this.loadChartData();
			},
			
			loadChartData() {
				if (!this.selectedTimeRange.startTime || !this.selectedTimeRange.endTime) {
					console.warn('时间范围未选择，无法调用API');
					return;
				}
				
				this.processedChartData = null;
				this.chartError = false;
				
				const params = {
					data: {
						name: this.chartData.name,
						unit: this.chartData.unit,
						deviceId: this.chartData.deviceId,
						identifier: this.chartData.identifier,
						startTime: this.selectedTimeRange.startTime,
						endTime: this.selectedTimeRange.endTime,
						displayStats: true,
						displayGrowth: true,
						clientType: "wechat"
					}
				};
				
				waterqualityConfigEcharts(params).then(res => {
					if (res.code === 200 && res.data) {
						this.processChartData(res.data);
					} else {
						console.error('API调用失败:', res);
						this.chartError = true;
					}
				}).catch(error => {
					console.error('API调用异常:', error);
					this.chartError = true;
				});
			},

			// 判断时间范围是否超过一天
			isTimeRangeMultipleDays() {
				if (!this.selectedTimeRange.startTime || !this.selectedTimeRange.endTime) {
					return false;
				}

				const startDate = new Date(this.selectedTimeRange.startTime);
				const endDate = new Date(this.selectedTimeRange.endTime);

				// 计算时间差（毫秒）
				const timeDiff = endDate.getTime() - startDate.getTime();
				// 转换为天数
				const daysDiff = timeDiff / (1000 * 60 * 60 * 24);

				// 如果超过1天，返回true
				return daysDiff > 1;
			},

			processChartData(apiData) {
				const xdata = [];
				const seriesData = [];

				// 判断时间范围是否超过一天
				const isMultipleDays = this.isTimeRangeMultipleDays();

				if (apiData.his && apiData.his.length > 0) {
					apiData.his.forEach(item => {
						if (item.time) {
							const date = new Date(item.time);
							let timeStr;

							if (isMultipleDays) {
								// 超过一天：显示月份+天+小时格式 (MM-DD HH)
								const month = String(date.getMonth() + 1).padStart(2, '0');
								const day = String(date.getDate()).padStart(2, '0');
								const hours = String(date.getHours()).padStart(2, '0');
								timeStr = `${month}-${day} ${hours}`;
							} else {
								// 一天内：显示小时:分钟格式 (HH:MM)
								const hours = String(date.getHours()).padStart(2, '0');
								const minutes = String(date.getMinutes()).padStart(2, '0');
								timeStr = `${hours}:${minutes}`;
							}

							xdata.push(timeStr);
							seriesData.push(item.value);
						}
					});
				}
				
				this.processedChartData = {
					xdata: xdata,
					series: [{
						name: apiData.name || '数据',
						data: seriesData
					}],
					title: apiData.name || '图表数据',
					unit: apiData.unit || '',
					legend: [apiData.name || '数据'],
					stats: {
						avg: apiData.avg,
						max: apiData.max,
						min: apiData.min
					}
				};
			},
			
			formatDate(date) {
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				return `${year}-${month}-${day}`;
			},
			
			initCalendar() {
				const now = new Date();
				const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
				
				const todayStr = this.formatDate(today);
				this.selectedTimeRange = {
					startTime: `${todayStr} 00:00:00`,
					endTime: `${todayStr} 23:59:59`
				};
				this.currentDateRange = todayStr;
			}
		},
		onLoad(option) {
			if (option.data) {
				try {
					const dataString = decodeURIComponent(option.data);
					this.chartData = JSON.parse(dataString);
					this.chartTitle = `[CIMS]${this.chartData.name}趋势`;
					
					this.initCalendar();
					setTimeout(() => {
						this.selectTimeRange('today');
					}, 200);
				} catch (error) {
					console.error('解析传入参数失败:', error);
					this.chartError = true;
				}
			}
		}
	};
</script>

<style scoped>
	.container {
		height: 100vh;
		background: linear-gradient(135deg, #0a0f1c 0%, #1a1f2e 50%, #0f1419 100%);
		overflow: hidden;
	}

	.time-selector-panel {
		padding: 20rpx;
		background: rgba(33, 148, 255, 0.1);
		border-bottom: 1px solid rgba(33, 148, 255, 0.3);
	}

	.time-selector {
		display: flex;
		align-items: center;
		justify-content: space-between;
		background: rgba(33, 148, 255, 0.15);
		border: 1px solid rgba(33, 148, 255, 0.4);
		border-radius: 12rpx;
		padding: 20rpx 24rpx;
		min-height: 80rpx;
	}

	.selector-text {
		font-size: 28rpx;
		font-weight: 500;
		color: #fff;
	}

	.calendar-icon {
		font-size: 32rpx;
		margin-left: 16rpx;
	}

	.time-range-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.8);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
	}

	.modal-content {
		background: linear-gradient(135deg, #1a1f2e 0%, #2a2f3e 100%);
		border: 1px solid rgba(33, 148, 255, 0.3);
		border-radius: 20rpx;
		width: 640rpx;
		max-height: 80vh;
		overflow-y: auto;
		box-shadow: 0 0 30rpx rgba(33, 148, 255, 0.3);
	}

	.modal-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx 40rpx;
		border-bottom: 1px solid rgba(33, 148, 255, 0.3);
		background: rgba(33, 148, 255, 0.1);
	}

	.modal-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #fff;
		text-shadow: 0 0 8rpx rgba(33, 148, 255, 0.5);
	}

	.modal-close {
		font-size: 48rpx;
		color: rgba(255, 255, 255, 0.7);
		font-weight: bold;
		padding: 10rpx;
		line-height: 1;
	}

	.quick-select {
		padding: 30rpx 40rpx;
		border-bottom: 1px solid rgba(33, 148, 255, 0.2);
	}

	.quick-title {
		font-size: 28rpx;
		color: #fff;
		margin-bottom: 20rpx;
		display: block;
	}

	.quick-buttons {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
	}

	.quick-btn {
		background: linear-gradient(135deg, rgba(33, 148, 255, 0.2) 0%, rgba(0, 255, 136, 0.1) 100%);
		border: 1px solid rgba(33, 148, 255, 0.4);
		color: #fff;
		padding: 20rpx 30rpx;
		border-radius: 12rpx;
		font-size: 26rpx;
		min-width: 120rpx;
		text-align: center;
		transition: all 0.3s ease;
	}

	.quick-btn:active {
		background: linear-gradient(135deg, rgba(33, 148, 255, 0.4) 0%, rgba(0, 255, 136, 0.2) 100%);
		transform: scale(0.95);
	}

	.custom-select {
		padding: 30rpx 40rpx;
	}

	.custom-title {
		font-size: 28rpx;
		color: #fff;
		margin-bottom: 20rpx;
		display: block;
	}

	.date-inputs {
		margin-bottom: 30rpx;
	}

	.date-input-group {
		margin-bottom: 20rpx;
	}

	.input-label {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.7);
		margin-bottom: 10rpx;
		display: block;
	}

	.date-input {
		display: flex;
		align-items: center;
		justify-content: space-between;
		background: rgba(33, 148, 255, 0.1);
		border: 1px solid rgba(33, 148, 255, 0.3);
		border-radius: 12rpx;
		padding: 20rpx 24rpx;
		min-height: 80rpx;
	}

	.date-text {
		font-size: 28rpx;
		color: #fff;
		flex: 1;
	}

	.date-icon {
		font-size: 32rpx;
		color: rgba(255, 255, 255, 0.7);
	}

	.action-buttons {
		display: flex;
		gap: 20rpx;
		justify-content: flex-end;
	}

	.action-btn {
		padding: 24rpx 40rpx;
		border-radius: 12rpx;
		font-size: 28rpx;
		text-align: center;
		min-width: 120rpx;
		font-weight: bold;
	}

	.confirm-btn {
		background: linear-gradient(135deg, #2194FF 0%, #00ff88 100%);
		color: #fff;
		box-shadow: 0 0 20rpx rgba(33, 148, 255, 0.5);
	}

	.cancel-btn {
		background: rgba(255, 255, 255, 0.1);
		border: 1px solid rgba(255, 255, 255, 0.3);
		color: #fff;
	}

	.scroll-content {
		height: calc(100vh - 160rpx);
		overflow-y: auto;
	}

	.chart-container {
		padding: 20rpx;
	}

	.chart-item {
		background: linear-gradient(135deg, rgba(33, 148, 255, 0.1) 0%, rgba(0, 255, 136, 0.05) 100%);
		border: 1px solid rgba(33, 148, 255, 0.3);
		border-radius: 16rpx;
		overflow: hidden;
	}

	.chart-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx 28rpx;
		background: rgba(33, 148, 255, 0.15);
		border-bottom: 1px solid rgba(33, 148, 255, 0.3);
	}

	.chart-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #fff;
		text-shadow: 0 0 8rpx rgba(33, 148, 255, 0.5);
	}

	.chart-unit {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.7);
		background: rgba(0, 255, 136, 0.2);
		padding: 6rpx 12rpx;
		border-radius: 20rpx;
		border: 1px solid rgba(0, 255, 136, 0.3);
	}

	.chart-wrapper {
		height: 400rpx;
		position: relative;
		background: rgba(0, 0, 0, 0.2);
	}

	.chart-loading {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 100%;
		color: rgba(255, 255, 255, 0.6);
		font-size: 28rpx;
	}

	.chart-error {
		padding: 30rpx;
		color: #fff;
		display: flex;
		flex-direction: column;
		gap: 15rpx;
		text-align: center;
		align-items: center;
		justify-content: center;
		height: 100%;
	}

	.chart-error text:first-child {
		font-size: 28rpx;
		color: #ff6b35;
		font-weight: bold;
	}

	.error-detail {
		font-size: 22rpx;
		color: rgba(255, 255, 255, 0.6);
	}

	.chart-stats {
		display: flex;
		justify-content: space-around;
		padding: 20rpx 28rpx;
		background: rgba(0, 0, 0, 0.3);
		border-top: 1px solid rgba(33, 148, 255, 0.3);
	}

	.stats-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 8rpx;
	}

	.stats-label {
		font-size: 22rpx;
		color: rgba(255, 255, 255, 0.6);
	}

	.stats-value {
		font-size: 26rpx;
		font-weight: bold;
		color: #00ff88;
		font-family: 'Courier New', monospace;
	}
</style>