import constant from '@/utils/constant'
import storage from '@/utils/storage'
import { getClientId } from '@/utils/auth'
import { pullDeviceInfo } from '@/api/system/device'

function getDeviceInfo(page,device,state,time) {
		const deviceId=device.deviceId
		const devices= state.pullDevices[page];
		if(!devices){
		  return;
		}
		const idx=devices.findIndex(d=>d.deviceId==deviceId);
		if(idx==-1){
		  return;
		}
		const addTime=devices[idx].time;
		if(time && time<addTime){
		  return;
		}
		const lastTime=new Date().getTime();
		pullDeviceInfo({deviceId:device.deviceId,clientId:getClientId(),page:page}).then(res => {
			if(res.type=="property"){
			  console.log("set property,from",device.property,"to",res.data);
			  //属性更新
			  for(let p in res.data){
				if(device.property){
				  device.property[p]=res.data[p];
				}
			  }
			}else if(res.type=="state"){
			  device.online=res.identifier=="online";
			}
			//继续拉取
			getDeviceInfo(page,device,state,lastTime);
		})
}

const device = {
	state: {
		devices: storage.get(constant.device),
		pullDevices:{}
	},

	mutations: {
		SET_DEVICES: (state, devices) => {
			state.devices = devices
			storage.set(constant.device, devices)
		},
		SET_PULLDEVICES: (state, dataList) => {
			state.pullDevices[dataList[0]] = dataList[1]
			storage.set(constant.pullDevices, state.pullDevices)
		}
	},
	actions: {
		// 添加拉取
		addPullDevice({
			commit,
			state
		}, pageInfo) {
			let devices= state.pullDevices[pageInfo.page]
			if(devices){
				const idx=devices.findIndex(d=>d.deviceId==pageInfo.deviceId);
				if(idx>=0){
				  devices.splice(idx,1);
				}
				devices.push({deviceId:pageInfo.deviceId,time:new Date().getTime()});

			}else{
				devices=[{deviceId:pageInfo.deviceId,time:new Date().getTime()}]
			}
			commit('SET_PULLDEVICES', [pageInfo.page,devices])
		},
		// 移除拉取
		removePullDevice({
			commit,
			state
		}, pageInfo) {
			console.log("关闭拉取")
			if(!pageInfo.deviceId){
				delete state.pullDevices[pageInfo.page]
			}else{
				let devices= state.pullDevices[pageInfo.page];
				const idx=devices.findIndex(d=>d.deviceId==pageInfo.deviceId);
				if(idx>=0){
				  devices.splice(idx,1);
				}
			}
		},
		// 拉取信息
		pullDeviceInfo({
			commit,
			state
		}, pageInfo) {
			getDeviceInfo(pageInfo.page,pageInfo.device,state)
		}
	}
}

export default device
