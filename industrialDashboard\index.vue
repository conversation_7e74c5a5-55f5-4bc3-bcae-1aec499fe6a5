<template>
  <view class="industrial-dashboard">
    <!-- 自定义头部 -->
    <view class="dashboard-header">
      <view class="header-bg"></view>
      <view class="header-content">
        <view class="header-left">
          <view class="facility-info">
            <view class="facility-name">智能制造中心 A区</view>
            <view class="facility-status">
              <view class="status-dot running"></view>
              <text>运行中</text>
            </view>
          </view>
        </view>
        <view class="header-right">
          <view class="time-display">
            <text class="current-time">{{ currentTime }}</text>
            <text class="current-date">{{ currentDate }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <scroll-view class="dashboard-content" scroll-y="true">
      <!-- 关键指标卡片区 -->
      <view class="metrics-section">
        <view class="section-title">
          <view class="title-line"></view>
          <text>关键指标监控</text>
          <view class="title-decoration"></view>
        </view>
        
        <view class="metrics-grid">
          <view class="metric-card" v-for="(metric, index) in keyMetrics" :key="index">
            <view class="card-header">
              <view class="metric-icon" :class="metric.iconClass">
                <text class="iconfont" :class="metric.icon"></text>
              </view>
              <view class="metric-status" :class="metric.status">{{ metric.statusText }}</view>
            </view>
            <view class="card-body">
              <view class="metric-value">
                <text class="value">{{ metric.value }}</text>
                <text class="unit">{{ metric.unit }}</text>
              </view>
              <view class="metric-name">{{ metric.name }}</view>
              <view class="metric-trend" :class="metric.trend">
                <text class="trend-icon">{{ metric.trendIcon }}</text>
                <text class="trend-text">{{ metric.trendText }}</text>
              </view>
            </view>
            <view class="card-footer">
              <view class="progress-bar">
                <view class="progress-fill" :style="{ width: metric.progress + '%' }"></view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 设备状态监控区 -->
      <view class="equipment-section">
        <view class="section-title">
          <view class="title-line"></view>
          <text>设备状态监控</text>
          <view class="title-decoration"></view>
        </view>
        
        <view class="equipment-grid">
          <view class="equipment-card" v-for="(equipment, index) in equipmentList" :key="index">
            <view class="equipment-header">
              <view class="equipment-name">{{ equipment.name }}</view>
              <view class="equipment-id">ID: {{ equipment.id }}</view>
            </view>
            <view class="equipment-body">
              <view class="equipment-visual">
                <view class="equipment-icon" :class="equipment.statusClass">
                  <view class="icon-inner"></view>
                  <view class="pulse-ring" v-if="equipment.status === 'running'"></view>
                </view>
                <view class="connection-line" :class="equipment.statusClass"></view>
              </view>
              <view class="equipment-info">
                <view class="info-row">
                  <text class="label">温度:</text>
                  <text class="value" :class="equipment.tempStatus">{{ equipment.temperature }}°C</text>
                </view>
                <view class="info-row">
                  <text class="label">压力:</text>
                  <text class="value">{{ equipment.pressure }} MPa</text>
                </view>
                <view class="info-row">
                  <text class="label">运行时间:</text>
                  <text class="value">{{ equipment.runtime }}h</text>
                </view>
              </view>
            </view>
            <view class="equipment-footer">
              <view class="status-indicator" :class="equipment.statusClass">
                <text>{{ equipment.statusText }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 实时数据流区 -->
      <view class="datastream-section">
        <view class="section-title">
          <view class="title-line"></view>
          <text>实时数据流</text>
          <view class="title-decoration"></view>
        </view>
        
        <view class="datastream-container">
          <view class="stream-header">
            <view class="stream-controls">
              <view class="control-btn active">实时</view>
              <view class="control-btn">历史</view>
              <view class="control-btn">分析</view>
            </view>
            <view class="stream-status">
              <view class="status-dot active"></view>
              <text>数据流活跃</text>
            </view>
          </view>
          
          <view class="stream-content">
            <view class="data-item" v-for="(data, index) in realtimeData" :key="index">
              <view class="data-timestamp">{{ data.timestamp }}</view>
              <view class="data-source">{{ data.source }}</view>
              <view class="data-value" :class="data.type">
                <text class="value">{{ data.value }}</text>
                <text class="unit">{{ data.unit }}</text>
              </view>
              <view class="data-indicator" :class="data.status"></view>
            </view>
          </view>
        </view>
      </view>

      <!-- 系统控制面板 -->
      <view class="control-section">
        <view class="section-title">
          <view class="title-line"></view>
          <text>系统控制面板</text>
          <view class="title-decoration"></view>
        </view>
        
        <view class="control-panel">
          <view class="control-group" v-for="(group, index) in controlGroups" :key="index">
            <view class="group-title">{{ group.title }}</view>
            <view class="control-buttons">
              <view class="control-btn-large" 
                    v-for="(btn, btnIndex) in group.buttons" 
                    :key="btnIndex"
                    :class="[btn.type, { active: btn.active }]"
                    @click="handleControlAction(btn)">
                <view class="btn-icon">
                  <text class="iconfont" :class="btn.icon"></text>
                </view>
                <view class="btn-text">{{ btn.text }}</view>
                <view class="btn-status" v-if="btn.status">{{ btn.status }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部快速操作栏 -->
    <view class="quick-actions">
      <view class="action-btn" v-for="(action, index) in quickActions" :key="index" @click="handleQuickAction(action)">
        <view class="action-icon" :class="action.type">
          <text class="iconfont" :class="action.icon"></text>
        </view>
        <text class="action-text">{{ action.text }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentTime: '',
      currentDate: '',
      keyMetrics: [
        {
          name: '生产效率',
          value: '87.5',
          unit: '%',
          status: 'normal',
          statusText: '正常',
          trend: 'up',
          trendIcon: '↗',
          trendText: '+2.3%',
          progress: 87.5,
          icon: 'icon-efficiency',
          iconClass: 'efficiency'
        },
        {
          name: '能耗指标',
          value: '342.8',
          unit: 'kWh',
          status: 'warning',
          statusText: '注意',
          trend: 'up',
          trendIcon: '↗',
          trendText: '****%',
          progress: 68.5,
          icon: 'icon-energy',
          iconClass: 'energy'
        },
        {
          name: '质量合格率',
          value: '99.2',
          unit: '%',
          status: 'excellent',
          statusText: '优秀',
          trend: 'stable',
          trendIcon: '→',
          trendText: '稳定',
          progress: 99.2,
          icon: 'icon-quality',
          iconClass: 'quality'
        },
        {
          name: '设备利用率',
          value: '76.3',
          unit: '%',
          status: 'normal',
          statusText: '正常',
          trend: 'down',
          trendIcon: '↘',
          trendText: '-1.8%',
          progress: 76.3,
          icon: 'icon-utilization',
          iconClass: 'utilization'
        }
      ],
      equipmentList: [
        {
          name: '主生产线A',
          id: 'PL001',
          status: 'running',
          statusText: '运行中',
          statusClass: 'running',
          temperature: 75,
          tempStatus: 'normal',
          pressure: 2.3,
          runtime: 156
        },
        {
          name: '辅助设备B',
          id: 'AE002',
          status: 'standby',
          statusText: '待机',
          statusClass: 'standby',
          temperature: 45,
          tempStatus: 'normal',
          pressure: 1.8,
          runtime: 89
        },
        {
          name: '质检设备C',
          id: 'QC003',
          status: 'maintenance',
          statusText: '维护中',
          statusClass: 'maintenance',
          temperature: 32,
          tempStatus: 'low',
          pressure: 0.5,
          runtime: 203
        }
      ],
      realtimeData: [
        { timestamp: '14:23:45', source: '传感器A01', value: '23.5', unit: '°C', type: 'temperature', status: 'normal' },
        { timestamp: '14:23:44', source: '压力表B02', value: '2.34', unit: 'MPa', type: 'pressure', status: 'normal' },
        { timestamp: '14:23:43', source: '流量计C03', value: '145.2', unit: 'L/min', type: 'flow', status: 'high' },
        { timestamp: '14:23:42', source: '振动传感器D04', value: '0.08', unit: 'mm/s', type: 'vibration', status: 'normal' },
        { timestamp: '14:23:41', source: '电流表E05', value: '15.6', unit: 'A', type: 'current', status: 'normal' }
      ],
      controlGroups: [
        {
          title: '主要控制',
          buttons: [
            { text: '启动系统', icon: 'icon-play', type: 'primary', active: true, status: '运行中' },
            { text: '暂停操作', icon: 'icon-pause', type: 'warning', active: false },
            { text: '紧急停止', icon: 'icon-stop', type: 'danger', active: false }
          ]
        },
        {
          title: '模式切换',
          buttons: [
            { text: '自动模式', icon: 'icon-auto', type: 'success', active: true },
            { text: '手动模式', icon: 'icon-manual', type: 'default', active: false },
            { text: '维护模式', icon: 'icon-maintenance', type: 'secondary', active: false }
          ]
        }
      ],
      quickActions: [
        { text: '告警', icon: 'icon-alarm', type: 'alarm' },
        { text: '报告', icon: 'icon-report', type: 'report' },
        { text: '设置', icon: 'icon-setting', type: 'setting' },
        { text: '帮助', icon: 'icon-help', type: 'help' }
      ]
    };
  },
  mounted() {
    this.updateTime();
    this.timeInterval = setInterval(this.updateTime, 1000);
    this.dataInterval = setInterval(this.updateRealtimeData, 2000);
  },
  beforeDestroy() {
    if (this.timeInterval) clearInterval(this.timeInterval);
    if (this.dataInterval) clearInterval(this.dataInterval);
  },
  methods: {
    updateTime() {
      const now = new Date();
      this.currentTime = now.toTimeString().slice(0, 8);
      this.currentDate = now.toLocaleDateString('zh-CN', { 
        year: 'numeric', 
        month: '2-digit', 
        day: '2-digit' 
      });
    },
    updateRealtimeData() {
      // 模拟实时数据更新
      const newData = {
        timestamp: new Date().toTimeString().slice(0, 8),
        source: '传感器' + String.fromCharCode(65 + Math.floor(Math.random() * 5)) + '0' + (Math.floor(Math.random() * 9) + 1),
        value: (Math.random() * 100).toFixed(1),
        unit: ['°C', 'MPa', 'L/min', 'mm/s', 'A'][Math.floor(Math.random() * 5)],
        type: ['temperature', 'pressure', 'flow', 'vibration', 'current'][Math.floor(Math.random() * 5)],
        status: ['normal', 'high', 'low'][Math.floor(Math.random() * 3)]
      };
      this.realtimeData.unshift(newData);
      if (this.realtimeData.length > 8) {
        this.realtimeData.pop();
      }
    },
    handleControlAction(btn) {
      console.log('控制操作:', btn);
      // 这里可以添加实际的控制逻辑
    },
    handleQuickAction(action) {
      console.log('快速操作:', action);
      // 这里可以添加快速操作逻辑
    }
  }
};
</script>

<style lang="scss" scoped>
.industrial-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #0c1425 0%, #1a2332 50%, #0f1419 100%);
  position: relative;
  overflow: hidden;
}

// 头部样式
.dashboard-header {
  position: relative;
  height: 120rpx;
  padding: 0 30rpx;
  
  .header-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(33, 148, 255, 0.1) 0%, rgba(33, 148, 255, 0.05) 100%);
    border-bottom: 2rpx solid rgba(33, 148, 255, 0.3);
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2rpx;
      background: linear-gradient(90deg, transparent 0%, #2194FF 50%, transparent 100%);
    }
  }
  
  .header-content {
    position: relative;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: 2;
  }
  
  .facility-info {
    .facility-name {
      font-size: 32rpx;
      font-weight: 600;
      color: #FFFFFF;
      margin-bottom: 8rpx;
    }
    
    .facility-status {
      display: flex;
      align-items: center;
      font-size: 24rpx;
      color: #00FF88;
      
      .status-dot {
        width: 12rpx;
        height: 12rpx;
        border-radius: 50%;
        margin-right: 12rpx;
        
        &.running {
          background: #00FF88;
          box-shadow: 0 0 20rpx rgba(0, 255, 136, 0.5);
          animation: pulse 2s infinite;
        }
      }
    }
  }
  
  .time-display {
    text-align: right;
    
    .current-time {
      display: block;
      font-size: 28rpx;
      font-weight: 500;
      color: #FFFFFF;
      font-family: 'Courier New', monospace;
    }
    
    .current-date {
      display: block;
      font-size: 22rpx;
      color: rgba(255, 255, 255, 0.7);
      margin-top: 4rpx;
    }
  }
}

// 内容区域
.dashboard-content {
  height: calc(100vh - 120rpx - 120rpx);
  padding: 30rpx;
}

// 章节标题样式
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  
  .title-line {
    width: 8rpx;
    height: 32rpx;
    background: linear-gradient(180deg, #2194FF 0%, #00FF88 100%);
    border-radius: 4rpx;
    margin-right: 20rpx;
  }
  
  text {
    font-size: 32rpx;
    font-weight: 600;
    color: #FFFFFF;
    flex: 1;
  }
  
  .title-decoration {
    width: 100rpx;
    height: 2rpx;
    background: linear-gradient(90deg, transparent 0%, rgba(33, 148, 255, 0.5) 100%);
  }
}

// 关键指标卡片
.metrics-section {
  margin-bottom: 50rpx;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.metric-card {
  background: linear-gradient(135deg, rgba(26, 35, 50, 0.8) 0%, rgba(15, 20, 25, 0.9) 100%);
  border: 1rpx solid rgba(33, 148, 255, 0.2);
  border-radius: 16rpx;
  padding: 24rpx;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2rpx;
    background: linear-gradient(90deg, transparent 0%, #2194FF 50%, transparent 100%);
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    
    .metric-icon {
      width: 48rpx;
      height: 48rpx;
      border-radius: 12rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      
      &.efficiency { background: linear-gradient(135deg, #FF6B6B, #FF8E53); }
      &.energy { background: linear-gradient(135deg, #4ECDC4, #44A08D); }
      &.quality { background: linear-gradient(135deg, #45B7D1, #96C93D); }
      &.utilization { background: linear-gradient(135deg, #FFA726, #FB8C00); }
      
      .iconfont {
        font-size: 24rpx;
        color: #FFFFFF;
      }
    }
    
    .metric-status {
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      font-size: 20rpx;
      font-weight: 500;
      
      &.normal { background: rgba(0, 255, 136, 0.2); color: #00FF88; }
      &.warning { background: rgba(255, 193, 7, 0.2); color: #FFC107; }
      &.excellent { background: rgba(76, 175, 80, 0.2); color: #4CAF50; }
    }
  }
  
  .card-body {
    .metric-value {
      display: flex;
      align-items: baseline;
      margin-bottom: 12rpx;
      
      .value {
        font-size: 48rpx;
        font-weight: 700;
        color: #FFFFFF;
        line-height: 1;
      }
      
      .unit {
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.7);
        margin-left: 8rpx;
      }
    }
    
    .metric-name {
      font-size: 24rpx;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 16rpx;
    }
    
    .metric-trend {
      display: flex;
      align-items: center;
      font-size: 22rpx;
      
      &.up { color: #00FF88; }
      &.down { color: #FF6B6B; }
      &.stable { color: #FFC107; }
      
      .trend-icon {
        margin-right: 8rpx;
        font-weight: bold;
      }
    }
  }
  
  .card-footer {
    margin-top: 20rpx;
    
    .progress-bar {
      height: 6rpx;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 3rpx;
      overflow: hidden;
      
      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #2194FF, #00FF88);
        border-radius: 3rpx;
        transition: width 0.3s ease;
      }
    }
  }
}

// 设备监控区域
.equipment-section {
  margin-bottom: 50rpx;
}

.equipment-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.equipment-card {
  background: linear-gradient(135deg, rgba(26, 35, 50, 0.8) 0%, rgba(15, 20, 25, 0.9) 100%);
  border: 1rpx solid rgba(33, 148, 255, 0.2);
  border-radius: 16rpx;
  padding: 24rpx;
  
  .equipment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    
    .equipment-name {
      font-size: 28rpx;
      font-weight: 600;
      color: #FFFFFF;
    }
    
    .equipment-id {
      font-size: 22rpx;
      color: rgba(255, 255, 255, 0.6);
      font-family: 'Courier New', monospace;
    }
  }
  
  .equipment-body {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    
    .equipment-visual {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 30rpx;
      
      .equipment-icon {
        position: relative;
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 10rpx;
        
        &.running {
          background: linear-gradient(135deg, #00FF88, #00C4A7);
          box-shadow: 0 0 30rpx rgba(0, 255, 136, 0.3);
        }
        
        &.standby {
          background: linear-gradient(135deg, #FFC107, #FF9800);
          box-shadow: 0 0 30rpx rgba(255, 193, 7, 0.3);
        }
        
        &.maintenance {
          background: linear-gradient(135deg, #FF6B6B, #E53E3E);
          box-shadow: 0 0 30rpx rgba(255, 107, 107, 0.3);
        }
        
        .icon-inner {
          width: 24rpx;
          height: 24rpx;
          background: #FFFFFF;
          border-radius: 50%;
        }
        
        .pulse-ring {
          position: absolute;
          top: -10rpx;
          left: -10rpx;
          right: -10rpx;
          bottom: -10rpx;
          border: 2rpx solid rgba(0, 255, 136, 0.4);
          border-radius: 50%;
          animation: pulse-ring 2s infinite;
        }
      }
      
      .connection-line {
        width: 4rpx;
        height: 40rpx;
        
        &.running { background: linear-gradient(180deg, #00FF88, transparent); }
        &.standby { background: linear-gradient(180deg, #FFC107, transparent); }
        &.maintenance { background: linear-gradient(180deg, #FF6B6B, transparent); }
      }
    }
    
    .equipment-info {
      flex: 1;
      
      .info-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12rpx;
        
        .label {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.7);
        }
        
        .value {
          font-size: 24rpx;
          color: #FFFFFF;
          font-weight: 500;
          
          &.normal { color: #00FF88; }
          &.high { color: #FF6B6B; }
          &.low { color: #FFC107; }
        }
      }
    }
  }
  
  .equipment-footer {
    .status-indicator {
      padding: 12rpx 20rpx;
      border-radius: 20rpx;
      text-align: center;
      font-size: 22rpx;
      font-weight: 500;
      
      &.running { background: rgba(0, 255, 136, 0.2); color: #00FF88; }
      &.standby { background: rgba(255, 193, 7, 0.2); color: #FFC107; }
      &.maintenance { background: rgba(255, 107, 107, 0.2); color: #FF6B6B; }
    }
  }
}

// 实时数据流
.datastream-section {
  margin-bottom: 50rpx;
}

.datastream-container {
  background: linear-gradient(135deg, rgba(26, 35, 50, 0.8) 0%, rgba(15, 20, 25, 0.9) 100%);
  border: 1rpx solid rgba(33, 148, 255, 0.2);
  border-radius: 16rpx;
  padding: 24rpx;
  
  .stream-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;
    
    .stream-controls {
      display: flex;
      gap: 16rpx;
      
      .control-btn {
        padding: 12rpx 24rpx;
        border-radius: 20rpx;
        font-size: 22rpx;
        background: rgba(255, 255, 255, 0.1);
        color: rgba(255, 255, 255, 0.7);
        transition: all 0.3s ease;
        
        &.active {
          background: linear-gradient(135deg, #2194FF, #00FF88);
          color: #FFFFFF;
        }
      }
    }
    
    .stream-status {
      display: flex;
      align-items: center;
      font-size: 22rpx;
      color: #00FF88;
      
      .status-dot {
        width: 12rpx;
        height: 12rpx;
        border-radius: 50%;
        margin-right: 12rpx;
        
        &.active {
          background: #00FF88;
          box-shadow: 0 0 20rpx rgba(0, 255, 136, 0.5);
          animation: pulse 2s infinite;
        }
      }
    }
  }
  
  .stream-content {
    max-height: 300rpx;
    overflow-y: auto;
    
    .data-item {
      display: flex;
      align-items: center;
      padding: 16rpx 0;
      border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
      
      &:last-child {
        border-bottom: none;
      }
      
      .data-timestamp {
        font-size: 20rpx;
        color: rgba(255, 255, 255, 0.6);
        font-family: 'Courier New', monospace;
        width: 120rpx;
      }
      
      .data-source {
        font-size: 22rpx;
        color: rgba(255, 255, 255, 0.8);
        width: 150rpx;
      }
      
      .data-value {
        flex: 1;
        display: flex;
        align-items: baseline;
        
        .value {
          font-size: 24rpx;
          font-weight: 600;
          color: #FFFFFF;
        }
        
        .unit {
          font-size: 18rpx;
          color: rgba(255, 255, 255, 0.6);
          margin-left: 8rpx;
        }
        
        &.temperature .value { color: #FF6B6B; }
        &.pressure .value { color: #4ECDC4; }
        &.flow .value { color: #45B7D1; }
        &.vibration .value { color: #FFA726; }
        &.current .value { color: #9C27B0; }
      }
      
      .data-indicator {
        width: 16rpx;
        height: 16rpx;
        border-radius: 50%;
        
        &.normal { background: #00FF88; }
        &.high { background: #FF6B6B; }
        &.low { background: #FFC107; }
      }
    }
  }
}

// 控制面板
.control-section {
  margin-bottom: 50rpx;
}

.control-panel {
  .control-group {
    margin-bottom: 30rpx;
    
    .group-title {
      font-size: 26rpx;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 20rpx;
    }
    
    .control-buttons {
      display: flex;
      gap: 16rpx;
      flex-wrap: wrap;
      
      .control-btn-large {
        flex: 1;
        min-width: 200rpx;
        background: linear-gradient(135deg, rgba(26, 35, 50, 0.8) 0%, rgba(15, 20, 25, 0.9) 100%);
        border: 1rpx solid rgba(33, 148, 255, 0.2);
        border-radius: 16rpx;
        padding: 24rpx;
        text-align: center;
        position: relative;
        transition: all 0.3s ease;
        
        &.primary.active {
          background: linear-gradient(135deg, #2194FF, #1976D2);
          border-color: #2194FF;
          box-shadow: 0 8rpx 25rpx rgba(33, 148, 255, 0.3);
        }
        
        &.warning.active {
          background: linear-gradient(135deg, #FFC107, #FF9800);
          border-color: #FFC107;
        }
        
        &.danger {
          background: linear-gradient(135deg, rgba(255, 107, 107, 0.2), rgba(229, 62, 62, 0.2));
          border-color: rgba(255, 107, 107, 0.3);
        }
        
        &.success.active {
          background: linear-gradient(135deg, #00FF88, #00C4A7);
          border-color: #00FF88;
        }
        
        .btn-icon {
          width: 48rpx;
          height: 48rpx;
          margin: 0 auto 12rpx;
          border-radius: 12rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(255, 255, 255, 0.1);
          
          .iconfont {
            font-size: 24rpx;
            color: #FFFFFF;
          }
        }
        
        .btn-text {
          font-size: 24rpx;
          color: #FFFFFF;
          font-weight: 500;
          margin-bottom: 8rpx;
        }
        
        .btn-status {
          font-size: 20rpx;
          color: rgba(255, 255, 255, 0.7);
        }
      }
    }
  }
}

// 快速操作栏
.quick-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background: linear-gradient(135deg, rgba(26, 35, 50, 0.95) 0%, rgba(15, 20, 25, 0.95) 100%);
  backdrop-filter: blur(10px);
  border-top: 1rpx solid rgba(33, 148, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 0 30rpx;
  
  .action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16rpx;
    border-radius: 12rpx;
    transition: all 0.3s ease;
    
    &:active {
      background: rgba(33, 148, 255, 0.1);
    }
    
    .action-icon {
      width: 48rpx;
      height: 48rpx;
      border-radius: 12rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 8rpx;
      
      &.alarm { background: linear-gradient(135deg, #FF6B6B, #E53E3E); }
      &.report { background: linear-gradient(135deg, #4ECDC4, #44A08D); }
      &.setting { background: linear-gradient(135deg, #FFA726, #FB8C00); }
      &.help { background: linear-gradient(135deg, #9C27B0, #7B1FA2); }
      
      .iconfont {
        font-size: 24rpx;
        color: #FFFFFF;
      }
    }
    
    .action-text {
      font-size: 20rpx;
      color: rgba(255, 255, 255, 0.8);
    }
  }
}

// 动画效果
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

@keyframes pulse-ring {
  0% { transform: scale(1); opacity: 1; }
  100% { transform: scale(1.3); opacity: 0; }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6rpx;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3rpx;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #2194FF, #00FF88);
  border-radius: 3rpx;
}
</style> 