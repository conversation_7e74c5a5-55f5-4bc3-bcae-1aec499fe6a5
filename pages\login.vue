<template>
	<view class="login-page">

		<view class="background-layer">
			<view class="grid-pattern"></view>
			<view class="floating-particles">
				<view class="particle" v-for="n in 20" :key="n" :style="getParticleStyle(n)"></view>
			</view>
		</view>


		<view class="login-container">

			<view class="logo-section">
				<view class="logo-wrapper">
					<view class="logo-icon">
						<view class="logo-inner">
							<text class="logo-text">CIMS</text>
						</view>
						<view class="logo-ring"></view>
						<view class="logo-ring-outer"></view>
					</view>
				</view>
				<view class="title-wrapper">
					<text class="main-title">智能运维平台</text>
					<text class="sub-title">Intelligent Operation & Maintenance Platform</text>
				</view>
			</view>


			<view class="form-section">
				<view class="form-container">
					<view class="form-header">
						<text class="form-title">用户登录</text>
						<view class="form-divider"></view>
					</view>

					<view class="input-group">
						<view class="input-wrapper">
							<view class="input-icon">
								<view class="iconfont icon-user"></view>
							</view>
							<input
								v-model="loginForm.username"
								class="form-input"
								type="text"
								placeholder="请输入账号"
								maxlength="30"
								:class="{ 'input-focused': usernameFocused }"
								@focus="usernameFocused = true"
								@blur="usernameFocused = false"
							/>
							<view class="input-border"></view>
						</view>

						<view class="input-wrapper">
							<view class="input-icon">
								<view class="iconfont icon-password"></view>
							</view>
							<input
								v-model="loginForm.password"
								class="form-input"
								type="password"
								placeholder="请输入密码"
								maxlength="20"
								:class="{ 'input-focused': passwordFocused }"
								@focus="passwordFocused = true"
								@blur="passwordFocused = false"
							/>
							<view class="input-border"></view>
						</view>
					</view>

					<view class="login-actions">
						<button
							@click="handleLogin"
							class="login-button"
							:class="{ 'loading': loading }"
							:disabled="loading"
						>
							<view v-if="loading" class="button-loading-spinner"></view>
							<text>{{ loading ? '登录中...' : '立即登录' }}</text>
							<view class="button-ripple" v-if="!loading"></view>
						</button>
					</view>
				</view>
			</view>
		</view>


		<view v-if="loading" class="loading-overlay">
			<view class="loading-container">
				<view class="loading-logo">
					<view class="loading-logo-inner">
						<text class="loading-logo-text">CIMS</text>
					</view>
					<view class="loading-ring-1"></view>
					<view class="loading-ring-2"></view>
					<view class="loading-ring-3"></view>
				</view>
				<view class="loading-text">
					<text class="loading-main-text">正在登录</text>
					<view class="loading-dots">
						<view class="dot"></view>
						<view class="dot"></view>
						<view class="dot"></view>
					</view>
				</view>
				<view class="loading-progress">
					<view class="progress-bar">
						<view class="progress-fill"></view>
					</view>
					<text class="progress-text">验证身份中...</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getCodeImg,
		getProject,
		accountLogin
	} from '@/api/login'
	import { Decrypt } from '@/utils/encryptionService'
	import {
		setProjectList,
		setProject,
		getToken,
		setToken,
		removeToken,
		setClientId,
		setRole
	} from '@/utils/auth'

	export default {
		data() {
			return {
				globalConfig: getApp().globalData.config,
				loginForm: {
					username: "",
					password: "",
					code: "",
					uuid: ''
				},
				loading: false,
				usernameFocused: false,
				passwordFocused: false
			}
		},
		mounted() {
			const savedUsername = uni.getStorageSync('savedUsername');
			const encryptedPassword = uni.getStorageSync('savedPassword');
			if (savedUsername) {
				this.loginForm.username = savedUsername;
			}
			if (encryptedPassword) {
				this.loginForm.password = Decrypt(encryptedPassword);
			}
		},
		methods: {
			getParticleStyle(index) {
				const size = Math.random() * 4 + 2;
				const left = Math.random() * 100;
				const animationDelay = Math.random() * 20;
				const animationDuration = Math.random() * 10 + 10;

				return {
					width: size + 'px',
					height: size + 'px',
					left: left + '%',
					animationDelay: animationDelay + 's',
					animationDuration: animationDuration + 's'
				};
			},


			async handleLogin() {
				if (this.loginForm.username === "") {
					this.$modal.msgError("请输入您的账号")
					return;
				}
				if (this.loginForm.password === "") {
					this.$modal.msgError("请输入您的密码")
					return;
				}
				
				this.loading = true;
				try {
					const res = await this.accountLogin();
					if (res.code === 200) {
						const token = res.data.token;
						setToken(token);
						this.$store.commit('SET_TOKEN', token);
						setClientId(new Date().getTime().toString());
						
						// 执行登录成功后的操作
						this.loginSuccess();
					} else {
						this.$modal.msgError(res.msg || '登录失败，请检查账号密码');
					}
				} catch (error) {
					console.error('登录失败:', error);
					this.$modal.msgError('登录失败，请检查网络连接');
				} finally {
					this.loading = false;
				}
			},
			

			async accountLogin() {
				return await this.$store.dispatch('AccountLogin', this.loginForm);
			},

			async loginSuccess(result) {
				console.log('获取用户信息');
				await this.$store.dispatch('GetInfo');

				try {
					const res = await getProject();
					console.log(res, '-=----');
					if (res.data.length === 0) {
						uni.showModal({
							title: '提示',
							content: '您没有项目权限，请联系管理员',
							showCancel: false,
							success: (res) => {
								if (res.confirm) {
									uni.reLaunch({
										url: '/pages/index'
									});
								}
							}
						});
					} else {
						setProjectList(res.data);
						setProject(res.data[0]);
					}
				} catch (error) {
					console.error('获取项目列表失败:', error);
				}


				this.$tab.reLaunch('/pages/index');
			}
		}
	}
</script>

<style lang="scss" scoped>
page {
	background-color: #0a0e1a;
}

.login-page {
	width: 100vw;
	height: 100vh;
	position: relative;
	overflow: hidden;
	background: linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 50%, #0f1419 100%);
}


.background-layer {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 1;
}

.grid-pattern {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-image:
		linear-gradient(rgba(33, 148, 255, 0.1) 1px, transparent 1px),
		linear-gradient(90deg, rgba(33, 148, 255, 0.1) 1px, transparent 1px);
	background-size: 50px 50px;
	animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
	0% { transform: translate(0, 0); }
	100% { transform: translate(50px, 50px); }
}

.floating-particles {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

.particle {
	position: absolute;
	background: rgba(33, 148, 255, 0.6);
	border-radius: 50%;
	animation: float linear infinite;
}

@keyframes float {
	0% {
		transform: translateY(100vh) rotate(0deg);
		opacity: 0;
	}
	10% {
		opacity: 1;
	}
	90% {
		opacity: 1;
	}
	100% {
		transform: translateY(-100px) rotate(360deg);
		opacity: 0;
	}
}


.login-container {
	position: relative;
	z-index: 2;
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	padding: 40rpx;
}


.logo-section {
	text-align: center;
	margin-bottom: 80rpx;
}

.logo-wrapper {
	position: relative;
	margin-bottom: 40rpx;
}

.logo-icon {
	position: relative;
	width: 120rpx;
	height: 120rpx;
	margin: 0 auto;
}

.logo-inner {
	width: 100%;
	height: 100%;
	background: linear-gradient(135deg, #2194ff, #00d4ff);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	z-index: 3;
	box-shadow: 0 0 30px rgba(33, 148, 255, 0.5);
}

.logo-text {
	color: #fff;
	font-size: 32rpx;
	font-weight: bold;
	text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.logo-ring {
	position: absolute;
	top: -10rpx;
	left: -10rpx;
	width: 140rpx;
	height: 140rpx;
	border: 2px solid rgba(33, 148, 255, 0.3);
	border-radius: 50%;
	animation: rotate 10s linear infinite;
}

.logo-ring-outer {
	position: absolute;
	top: -20rpx;
	left: -20rpx;
	width: 160rpx;
	height: 160rpx;
	border: 1px solid rgba(33, 148, 255, 0.2);
	border-radius: 50%;
	animation: rotate 15s linear infinite reverse;
}

@keyframes rotate {
	from { transform: rotate(0deg); }
	to { transform: rotate(360deg); }
}

.title-wrapper {
	text-align: center;
}

.main-title {
	display: block;
	font-size: 48rpx;
	font-weight: bold;
	color: #fff;
	margin-bottom: 10rpx;
	text-shadow: 0 0 20px rgba(33, 148, 255, 0.3);
}

.sub-title {
	display: block;
	font-size: 24rpx;
	color: #b9c8dc;
	letter-spacing: 2rpx;
}


.form-section {
	width: 100%;
	max-width: 600rpx;
}

.form-container {
	background: rgba(18, 34, 46, 0.8);
	border: 1px solid rgba(33, 148, 255, 0.3);
	border-radius: 20rpx;
	padding: 60rpx 40rpx;
	backdrop-filter: blur(10px);
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
	position: relative;
}

.form-container::before {
	content: '';
	position: absolute;
	top: -2px;
	left: -2px;
	right: -2px;
	bottom: -2px;
	background: linear-gradient(45deg, rgba(33, 148, 255, 0.3), transparent, rgba(33, 148, 255, 0.3));
	border-radius: 22rpx;
	z-index: -1;
}

.form-header {
	text-align: center;
	margin-bottom: 60rpx;
}

.form-title {
	font-size: 36rpx;
	color: #fff;
	font-weight: 500;
	margin-bottom: 20rpx;
}

.form-divider {
	width: 60rpx;
	height: 4rpx;
	background: linear-gradient(90deg, #2194ff, #00d4ff);
	margin: 0 auto;
	border-radius: 2rpx;
}

.input-group {
	margin-bottom: 60rpx;
}

.input-wrapper {
	position: relative;
	margin-bottom: 40rpx;
}

.input-icon {
	position: absolute;
	left: 30rpx;
	top: 50%;
	transform: translateY(-50%);
	z-index: 2;
}

.input-icon .iconfont {
	font-size: 32rpx;
	color: #b9c8dc;
	transition: color 0.3s ease;
}

.form-input {
	width: 100%;
	height: 88rpx;
	background: rgba(27, 34, 51, 0.8);
	border: 1px solid rgba(185, 200, 220, 0.2);
	border-radius: 44rpx;
	padding: 0 30rpx 0 80rpx;
	font-size: 28rpx;
	color: #fff;
	transition: all 0.3s ease;
}

.form-input::placeholder {
	color: #b9c8dc;
}

.form-input.input-focused {
	border-color: rgba(33, 148, 255, 0.6);
	box-shadow: 0 0 0 4rpx rgba(33, 148, 255, 0.1);
}

.form-input.input-focused + .input-border {
	transform: scaleX(1);
}

.input-focused .input-icon .iconfont {
	color: #2194ff;
}

.input-border {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 2rpx;
	background: linear-gradient(90deg, #2194ff, #00d4ff);
	transform: scaleX(0);
	transition: transform 0.3s ease;
	border-radius: 1rpx;
}

.login-actions {
	text-align: center;
}

.login-button {
	width: 100%;
	height: 88rpx;
	background: linear-gradient(135deg, #2194ff, #00d4ff);
	border: none;
	border-radius: 44rpx;
	font-size: 32rpx;
	color: #fff;
	font-weight: 500;
	position: relative;
	overflow: hidden;
	transition: all 0.3s ease;
	box-shadow: 0 4px 15px rgba(33, 148, 255, 0.3);
}

.login-button:hover {
	transform: translateY(-2rpx);
	box-shadow: 0 6px 20px rgba(33, 148, 255, 0.4);
}

.login-button.loading {
	background: rgba(33, 148, 255, 0.6);
	cursor: not-allowed;
	transform: none !important;
}

.login-button::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
	transition: left 0.5s ease;
}

.login-button:active::before {
	left: 100%;
}

.button-loading-spinner {
	width: 32rpx;
	height: 32rpx;
	border: 3rpx solid rgba(255, 255, 255, 0.3);
	border-top-color: #fff;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-right: 20rpx;
	display: inline-block;
}

.button-ripple {
	position: absolute;
	top: 50%;
	left: 50%;
	width: 0;
	height: 0;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.3);
	transform: translate(-50%, -50%);
	animation: ripple 0.6s ease-out;
}

@keyframes ripple {
	to {
		width: 300rpx;
		height: 300rpx;
		opacity: 0;
	}
}

@keyframes spin {
	to { transform: rotate(360deg); }
}


.loading-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	background: rgba(10, 14, 26, 0.95);
	backdrop-filter: blur(10px);
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;
	animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

.loading-container {
	text-align: center;
	animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
	from {
		transform: translateY(50rpx);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}

.loading-logo {
	position: relative;
	width: 160rpx;
	height: 160rpx;
	margin: 0 auto 60rpx;
}

.loading-logo-inner {
	width: 100%;
	height: 100%;
	background: linear-gradient(135deg, #2194ff, #00d4ff);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	z-index: 3;
	box-shadow: 0 0 40px rgba(33, 148, 255, 0.6);
	animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
	0%, 100% {
		transform: scale(1);
		box-shadow: 0 0 40px rgba(33, 148, 255, 0.6);
	}
	50% {
		transform: scale(1.05);
		box-shadow: 0 0 60px rgba(33, 148, 255, 0.8);
	}
}

.loading-logo-text {
	color: #fff;
	font-size: 36rpx;
	font-weight: bold;
	text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.loading-ring-1 {
	position: absolute;
	top: -15rpx;
	left: -15rpx;
	width: 190rpx;
	height: 190rpx;
	border: 3rpx solid transparent;
	border-top-color: rgba(33, 148, 255, 0.8);
	border-radius: 50%;
	animation: rotate 2s linear infinite;
}

.loading-ring-2 {
	position: absolute;
	top: -25rpx;
	left: -25rpx;
	width: 210rpx;
	height: 210rpx;
	border: 2rpx solid transparent;
	border-right-color: rgba(0, 212, 255, 0.6);
	border-radius: 50%;
	animation: rotate 3s linear infinite reverse;
}

.loading-ring-3 {
	position: absolute;
	top: -35rpx;
	left: -35rpx;
	width: 230rpx;
	height: 230rpx;
	border: 1rpx solid transparent;
	border-bottom-color: rgba(33, 148, 255, 0.4);
	border-radius: 50%;
	animation: rotate 4s linear infinite;
}

.loading-text {
	margin-bottom: 60rpx;
}

.loading-main-text {
	display: block;
	font-size: 32rpx;
	color: #fff;
	font-weight: 500;
	margin-bottom: 20rpx;
}

.loading-dots {
	display: flex;
	justify-content: center;
	gap: 10rpx;
}

.dot {
	width: 12rpx;
	height: 12rpx;
	background: #2194ff;
	border-radius: 50%;
	animation: dotBounce 1.4s ease-in-out infinite both;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }
.dot:nth-child(3) { animation-delay: 0s; }

@keyframes dotBounce {
	0%, 80%, 100% {
		transform: scale(0.8);
		opacity: 0.5;
	}
	40% {
		transform: scale(1.2);
		opacity: 1;
	}
}

.loading-progress {
	width: 400rpx;
	margin: 0 auto;
}

.progress-bar {
	width: 100%;
	height: 6rpx;
	background: rgba(185, 200, 220, 0.2);
	border-radius: 3rpx;
	overflow: hidden;
	margin-bottom: 20rpx;
}

.progress-fill {
	height: 100%;
	background: linear-gradient(90deg, #2194ff, #00d4ff);
	border-radius: 3rpx;
	animation: progressFill 2s ease-in-out infinite;
}

@keyframes progressFill {
	0% {
		width: 0%;
		transform: translateX(-100%);
	}
	50% {
		width: 100%;
		transform: translateX(0%);
	}
	100% {
		width: 100%;
		transform: translateX(100%);
	}
}

.progress-text {
	font-size: 24rpx;
	color: #b9c8dc;
	text-align: center;
}
</style>