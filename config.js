// 应用全局配置
module.exports = {
  mapKey: 'd44fbf530df1874a83b73653a2db086e',//添加自己的高德key
  // baseUrl: 'http://localhost:8086',
  // baseUrl: 'https://www.guopaismart.com/app-api',
  // baseUrl: 'https://exh.CIMS.com',
  // baseUrl: 'http://**************:8089',//
  baseUrl: 'http://**************:8089',//本地测试服务器
  // baseUrl: 'https://cimspro.huanjy.com/prod-api',//阿里云服务器
  // baseUrl: 'https://cimspro.huanjy.com',//阿里云服务器域名
  // baseUrl: 'http://*************:8089',

  // 应用信息
  appInfo: {
    // 应用名称
    name: "CIMS智能运维平台",
    // 应用版本（与manifest.json保持一致）
    version: "1.1.0",
    // 系统版本
    systemVersion: "v1.1.0"
  }
}
