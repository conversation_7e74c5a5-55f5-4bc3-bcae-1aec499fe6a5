<template>
  <view class="arr industrial-container">
    <AppHeader flex title="[CIMS]智能运维" />


    <view class="tab-panel tech-panel">
      <view class="tab-wrapper">
        <view
          v-for="(item, index) in subsectionList"
          :key="index"
          class="tab-item"
          :class="{ active: curNow === index }"
          @click="subsectionChange(index)"
        >
          <view class="tab-text">{{ item }}</view>
          <view class="tab-indicator"></view>
        </view>
      </view>
    </view>


    <view class="time-panel tech-panel">
      <view class="time-selector" @click="cliDateTime">
        <view class="time-icon">
          <text class="iconfont icon-time"></text>
        </view>
        <view class="time-text">{{ dateTimeShowvalue }}</view>
        <view class="time-arrow">
          <text class="iconfont icon-arrow-down"></text>
        </view>
      </view>
    </view>

    <u-datetime-picker
      v-if="dateTimeShow"
      :show="dateTimeShow"
      :value="dateTimeValue"
      mode="datetime"
      @confirm="dateTimeconfirm"
      @cancel="dateTimecancel"
    ></u-datetime-picker>
    <scroll-view class="scroll-content" scroll-y="true">

      <view v-if="curNow === 0" class="diagnosis-section">

        <EmptyData
          :show="diagnosisList.length === 0"
          title="暂无诊断数据"
          description="请选择时间范围获取智能诊断信息"
        />

        <view
          v-for="(item, index) in diagnosisList"
          :key="index"
          class="diagnosis-group"
        >
          <view
            class="group-header tech-panel"
            @click="toggleDiagnosisGroup(index)"
          >
            <view class="group-title">
              <view class="title-icon"></view>
              <text class="title-text">{{
                item.mainType == null ? "系统诊断" : item.mainType
              }}</text>
            </view>
            <view class="group-stats">
              <view class="stat-badge">
                <text class="stat-count">{{
                  item.analysisList ? item.analysisList.length : 0
                }}</text>
                <text class="stat-label">项目</text>
              </view>
              <view class="collapse-icon" :class="{ expanded: item.expanded }">
                <text class="iconfont icon-arrow-down"></text>
              </view>
            </view>
          </view>

          <view v-if="item.expanded !== false" class="diagnosis-content">
            <view
              v-for="(i, index1) in item.analysisList"
              :key="index1"
              class="analysis-card tech-card"
            >
              <view class="card-header">
                <view class="header-left">
                  <view
                    class="status-indicator"
                    :class="getStatusClass(i.leftContent)"
                  ></view>
                  <view class="analysis-title"
                    >{{ i.label }} - {{ i.title }}</view
                  >
                </view>
                <view class="header-right">
                  <view
                    class="diagnosis-status"
                    :class="getStatusClass(i.leftContent)"
                  >
                    {{ i.leftContent || "待诊断" }}
                  </view>
                </view>
              </view>

              <view class="card-content">
                <view class="metrics-grid">
                  <view class="metric-item">
                    <view class="metric-label">诊断结果</view>
                    <view
                      class="metric-value"
                      :class="getStatusClass(i.leftContent)"
                    >
                      {{ i.leftContent || "--" }}
                    </view>
                  </view>
                  <view class="metric-item">
                    <view class="metric-label">当前值</view>
                    <view class="metric-value">
                      <text>{{
                        i.centerContent === null ? "--" : i.centerContent
                      }}</text>
                      <text class="unit" v-if="i.centerContent !== null">{{
                        i.unit
                      }}</text>
                    </view>
                  </view>
                  <view class="metric-item">
                    <view class="metric-label">合理区间</view>
                    <view class="metric-value">{{
                      i.rightContent === null ? "--" : i.rightContent
                    }}</view>
                  </view>
                </view>


                <view
                  v-for="(l, index6) in i.reportList"
                  :key="index6"
                  class="report-section"
                >
                  <view class="report-tabs">
                    <view
                      v-for="(tab, tabIndex) in ['原因分析', '结论', '建议']"
                      :key="tabIndex"
                      class="report-tab"
                      :class="{
                        active:
                          activeReportTab ===
                          `${index}_${index1}_${index6}_${tabIndex}`,
                      }"
                      @click="
                        setActiveReportTab(
                          `${index}_${index1}_${index6}_${tabIndex}`
                        )
                      "
                    >
                      {{ tab }}
                    </view>
                  </view>

                  <view class="report-content">
                    <view
                      v-if="
                        activeReportTab === `${index}_${index1}_${index6}_0`
                      "
                      class="report-list"
                    >
                      <view
                        v-for="(reason, index2) in l.reason"
                        :key="index2"
                        class="report-item"
                      >
                        {{ reason }}
                      </view>
                    </view>
                    <view
                      v-if="
                        activeReportTab === `${index}_${index1}_${index6}_1`
                      "
                      class="report-list"
                    >
                      <view
                        v-for="(conclusion, index3) in l.conclusion"
                        :key="index3"
                        class="report-item"
                      >
                        {{ conclusion }}
                      </view>
                    </view>
                    <view
                      v-if="
                        activeReportTab === `${index}_${index1}_${index6}_2`
                      "
                      class="report-list"
                    >
                      <view
                        v-for="(suggestion, index4) in l.suggestion"
                        :key="index4"
                        class="report-item"
                      >
                        {{ suggestion }}
                      </view>
                    </view>
                  </view>
                </view>


                <view class="chart-section" v-if="i.chart">
                  <view class="chart-header">
                    <text class="chart-title">数据趋势</text>
                  </view>
                  <view class="chart-container">
                    <columnChart :data="i.chart"></columnChart>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view v-if="curNow === 1" class="indicators-section">

        <EmptyData
          :show="indicatorsContentList.length === 0"
          title="暂无指标数据"
          description="请选择时间范围获取关键指标分析"
        />

        <view
          v-for="(item, index) in indicatorsContentList"
          :key="index"
          class="indicator-group"
        >
          <view
            class="group-header tech-panel"
            @click="toggleIndicatorGroup(index)"
          >
            <view class="group-title">
              <view class="title-icon"></view>
              <text class="title-text">{{
                item.powerUnitName || "设备单元"
              }}</text>
            </view>
            <view class="group-stats">
              <view class="stat-badge">
                <text class="stat-count">{{
                  item.conclusionList ? item.conclusionList.length : 0
                }}</text>
                <text class="stat-label">指标</text>
              </view>
              <view class="collapse-icon" :class="{ expanded: item.expanded }">
                <text class="iconfont icon-arrow-down"></text>
              </view>
            </view>
          </view>

          <view v-if="item.expanded !== false" class="indicator-content">
            <view
              v-for="(i, index1) in item.conclusionList"
              :key="index1"
              class="indicator-card tech-card"
            >
              <view class="card-header">
                <view class="header-left">
                  <view
                    class="status-indicator"
                    :class="getIndicatorStatusClass(i.dataStatus)"
                  ></view>
                  <view class="indicator-type">{{ i.type || "未知类型" }}</view>
                </view>
                <view class="header-right">
                  <view
                    class="trend-indicator"
                    :class="getTrendClass(i.dataStatus)"
                  >
                    <img
                      v-if="i.dataStatus === 1"
                      class="trend-icon"
                      src="@/static/images/tabs/arrow.svg"
                      alt="上升"
                    />
                    <img
                      v-else-if="i.dataStatus === 3"
                      class="trend-icon"
                      src="@/static/images/tabs/arrowbottom.svg"
                      alt="下降"
                    />

                  </view>
                </view>
              </view>

              <view class="card-content">
                <view class="indicator-metrics">
                  <view class="metric-row">
                    <view class="metric-item">
                      <view class="metric-label">当前值</view>
                      <view class="metric-value current-value">
                        {{
                          i.currentValue === null ? "点位缺失" : i.currentValue
                        }}
                      </view>
                    </view>
                    <view class="metric-item">
                      <view class="metric-label">建议值</view>
                      <view class="metric-value suggested-value">
                        {{ i.suggestedValue || "--" }}
                      </view>
                    </view>
                  </view>

                  <view class="conclusion-section">
                    <view class="conclusion-label">分析结论</view>
                    <view class="conclusion-content">
                      {{ i.conclusion || "暂无结论" }}
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import columnChart from "./columnChart.vue";
import EmptyData from '@/components/EmptyData/EmptyData.vue';
import {
  getIntelligentDiagnosis,
  getIndicatorAnalysis,
} from "@/api/analysis/analysis";
import { getProject } from "@/utils/auth.js";
import { changeTimesStamp1, changeTimesStamp } from "@/utils/util.js";

export default {
  components: {
    columnChart,
    EmptyData
  },
  data() {
    return {
      diagnosisList: [],
      indicatorsContentList: [],
      curNow: 0,
      subsectionList: ["智能诊断", "关键指标分析"],
      chartData: {},
      dateTimeShow: false,
      endDateTime: "",
      startDateTime: "",
      dateTimeValue: new Date().getTime(),
      dateTimeShowvalue: changeTimesStamp1(Number(new Date())),
      iconColor: "#f5f5f5",
      iconShow: true,
      activeReportTab: "",
      TypetabList: [
        {
          name: "智能诊断",
          num: 1,
        },
        {
          name: "关键指标分析",
          num: 2,
        },
      ],
      systemTabList: [],
      inactiveStyle: {
        color: "#646566",
        fontWeight: "bold",
      },
      activeStyle: {
        color: "#f5f5f5",
        fontWeight: "bold",
        background: "#2194FF",
        padding: "10rpx",
        borderRadius: "15rpx",
      },
      activeStyle1: {
        color: "#f5f5f5",
        fontWeight: "bold",

      },

      project: {},
    };
  },
  onPullDownRefresh() {
    console.log("智能运维页面下拉刷新触发");
    const now = new Date();
    this.setTimeRange(now);

    setTimeout(() => {
      uni.stopPullDownRefresh();
    }, 300);
  },
  methods: {

    getStatusClass(status) {
      if (!status) return "normal";
      const statusStr = status.toString().toLowerCase();
      if (
        statusStr.includes("异常") ||
        statusStr.includes("故障") ||
        statusStr.includes("错误")
      ) {
        return "error";
      } else if (statusStr.includes("警告") || statusStr.includes("注意")) {
        return "warning";
      } else if (statusStr.includes("正常") || statusStr.includes("良好")) {
        return "success";
      }
      return "normal";
    },


    getIndicatorStatusClass(dataStatus) {
      if (dataStatus === 1) return "up";
      if (dataStatus === 3) return "down";
      return "stable";
    },


    getTrendClass(dataStatus) {
      if (dataStatus === 1) return "trend-up";
      if (dataStatus === 3) return "trend-down";
      return "trend-stable";
    },


    setActiveReportTab(tabId) {
      this.activeReportTab = tabId;
    },


    toggleIndicatorGroup(index) {
      const item = this.indicatorsContentList[index];

      if (item.expanded === undefined) {
        this.$set(item, "expanded", false);
      } else {
        this.$set(item, "expanded", !item.expanded);
      }
    },


    toggleDiagnosisGroup(index) {
      const item = this.diagnosisList[index];

      if (item.expanded === undefined) {
        this.$set(item, "expanded", false);
      } else {
        this.$set(item, "expanded", !item.expanded);
      }
    },

    subsectionChange(e) {
      this.curNow = e;
      console.log(this.curNow);
      if (this.curNow === 0) {
        this.getIntelligentDiagnosisList();
      } else if (this.curNow === 1) {
        this.getIndicatorAnalysisList();
      }
    },
    collapseChange(e) {
      console.log(e);
    },
    collapseClose(e) {
      console.log(e);
    },
    collapseOpen(e) {
      console.log(e);
    },

    typeHandleTabClick(e) {
      console.log(e);
    },
    systemHandleTabClick(e) {
      console.log(e);
    },

    getIntelligentDiagnosisList() {
      this.project = getProject();
      const params = {
        data: {
          start: this.startDateTime,
          end: this.endDateTime,
          configType: 7,
          projectId: this.project.id,
        },
      };
      getIntelligentDiagnosis(params).then((res) => {
        console.log(res);
        if (res.code == 200) {
          if (res.data.length > 0) {
            const processedData = JSON.parse(JSON.stringify(res.data));
            console.log(processedData, "processedData");
            processedData.forEach((mainTypeItem) => {

              mainTypeItem.analysisList.forEach((analysisItem) => {
                if (analysisItem.reportList === null) {
                  analysisItem.reportList = [];
                  return;
                }
                analysisItem.reportList.forEach((report) => {
                  report.reason = JSON.parse(report.reason);
                  report.conclusion = JSON.parse(report.conclusion);
                  report.suggestion = JSON.parse(report.suggestion);
                  if (report.reason.length === 0) {
                    report.reason = [];
                  } else {
                    report.reason = report.reason.map(
                      (item, index) => `${index + 1}. ${item}`
                    );
                  }
                  if (report.conclusion.length === 0) {
                    report.conclusion = [];
                  } else {
                    report.conclusion = report.conclusion.map(
                      (item, index) => `${index + 1}. ${item}`
                    );
                  }
                  if (report.suggestion.length === 0) {
                    report.suggestion = [];
                  } else {
                    report.suggestion = report.suggestion.map(
                      (item, index) => `${index + 1}. ${item}`
                    );
                  }


                });
              });
            });


            const dataWithExpanded = processedData.map((item) => {
              return {
                ...item,
                expanded: true,
              };
            });
            this.diagnosisList = dataWithExpanded;
            console.log(this.diagnosisList, "this.diagnosisList--");


            if (
              processedData.length > 0 &&
              processedData[0].analysisList &&
              processedData[0].analysisList.length > 0
            ) {
              this.activeReportTab = "0_0_0_0";
            }
          }
        } else {
          this.diagnosisList = [];
        }
      });
    },

    getIndicatorAnalysisList() {
      const params = {
        data: {
          start: this.startDateTime,
          end: this.endDateTime,
          configType: 7,
          projectId: this.project.id,
        },
      };
      getIndicatorAnalysis(params).then((res) => {
        console.log(res);
        if (res.code === 200) {
          if (res.data.length > 0) {

            const data = res.data.map((item) => {
              return {
                ...item,
                expanded: true,
              };
            });
            this.indicatorsContentList = data;
          } else {
            this.indicatorsContentList = [];
          }
        } else {
          this.indicatorsContentList = [];
        }
      });
    },
    setTimeRange(date) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      const hour = date.getHours().toString().padStart(2, "0");
      const minute = date.getMinutes();

      let startTime = "";
      let endTime = "";

      if (minute <= 30) {
        startTime = `${year}-${month}-${day} ${hour}:00:00`;
        endTime = `${year}-${month}-${day} ${hour}:30:00`;
      } else {
        startTime = `${year}-${month}-${day} ${hour}:30:00`;
        endTime = `${year}-${month}-${day} ${hour}:59:59`;
      }

      this.startDateTime = startTime;
      this.endDateTime = endTime;
      if (this.curNow === 0) {
        this.getIntelligentDiagnosisList();
      } else if (this.curNow === 1) {
        this.getIndicatorAnalysisList();
      }
    },
    dateTimeconfirm(e) {
      this.dateTimeShow = false;
      const selectedDate = new Date(e.value);
      this.setTimeRange(selectedDate);
      const year = selectedDate.getFullYear();
      const month = (selectedDate.getMonth() + 1).toString().padStart(2, "0");
      const day = selectedDate.getDate().toString().padStart(2, "0");
      const hour = selectedDate.getHours().toString().padStart(2, "0");
      const minute = selectedDate.getMinutes().toString().padStart(2, "0");
      this.dateTimeShowvalue = `${year}-${month}-${day} ${hour}:${minute}`;
    },
    dateTimecancel() {
      this.dateTimeShow = false;
    },
    cliDateTime() {
      this.dateTimeValue = new Date().getTime();
      this.dateTimeShow = true;
    },
  },
  mounted() {
    const now = new Date();
    this.setTimeRange(now);
  },
};
</script>

<style lang="scss" scoped>
.industrial-container {
  background: linear-gradient(
    180deg,
    rgba(16, 24, 36, 0.95) 0%,
    rgba(12, 20, 28, 0.98) 100%
  );
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.industrial-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: repeating-linear-gradient(
      0deg,
      transparent 0px,
      rgba(33, 148, 255, 0.03) 1px,
      transparent 2px
    ),
    repeating-linear-gradient(
      90deg,
      transparent 0px,
      rgba(33, 148, 255, 0.03) 1px,
      transparent 2px
    );
  background-size: 20px 20px;
  pointer-events: none;
}

.tech-panel {
  background: rgba(18, 34, 46, 0.7);
  border: 1px solid rgba(33, 148, 255, 0.3);
  border-radius: 8px;
  padding: 20rpx;
  position: relative;
  margin: 20rpx;
}

.tech-panel::before {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(90deg, rgba(33, 148, 255, 0.3), transparent);
  border-radius: 10px;
  z-index: -1;
}


.tab-panel {
  flex-shrink: 0;
  margin: 10rpx 20rpx 0 20rpx !important;
}

.tab-wrapper {
  display: flex;
  gap: 20rpx;
  justify-content: center;
}

.tab-item {
  position: relative;
  padding: 15rpx 30rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120rpx;
  text-align: center;
}

.tab-item.active {
  background: rgba(33, 148, 255, 0.2);
  border-radius: 8rpx;
}

.tab-text {
  font-size: 28rpx;
  color: #b9c8dc;
  transition: color 0.3s ease;
}

.tab-item.active .tab-text {
  color: #2194ff;
  font-weight: 500;
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 3rpx;
  background: linear-gradient(90deg, #2194ff, #00d4ff);
  border-radius: 2rpx;
  transition: width 0.3s ease;
}

.tab-item.active .tab-indicator {
  width: 80%;
}


.time-panel {
  flex-shrink: 0;
  margin: 10rpx 20rpx !important;
  display: flex;
  justify-content: center;
}

.time-selector {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding: 15rpx 25rpx;
  background: rgba(27, 34, 51, 0.8);
  border: 1px solid rgba(185, 200, 220, 0.2);
  border-radius: 25rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.time-selector:hover {
  border-color: rgba(33, 148, 255, 0.6);
  box-shadow: 0 0 0 4rpx rgba(33, 148, 255, 0.1);
}

.time-icon,
.time-arrow {
  font-size: 24rpx;
  color: #b9c8dc;
}

.time-text {
  font-size: 26rpx;
  color: #f5f5f5;
  min-width: 200rpx;
  text-align: center;
}

.scroll-content {
  flex: 1;
  overflow-y: auto;
}


.diagnosis-section {
  padding: 0 20rpx;
}

.diagnosis-group {
  margin-bottom: 40rpx;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.group-header.tech-panel {
  margin: 10rpx 0 20rpx 0 !important;
  cursor: pointer;
  transition: all 0.3s ease;
}

.group-header.tech-panel:hover {
  background: rgba(33, 148, 255, 0.1);
  transform: translateY(-1px);
}

.group-title {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.title-icon {
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(180deg, #2194ff, #00d4ff);
  border-radius: 4rpx;
}

.title-text {
  font-size: 32rpx;
  color: #f5f5f5;
  font-weight: 500;
}

.group-stats {
  display: flex;
  gap: 20rpx;
}

.stat-badge {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: rgba(33, 148, 255, 0.1);
  border: 1px solid rgba(33, 148, 255, 0.3);
  border-radius: 20rpx;
}

.stat-count {
  font-size: 24rpx;
  color: #2194ff;
  font-weight: bold;
}

.stat-label {
  font-size: 20rpx;
  color: #b9c8dc;
}

.collapse-icon {
  margin-left: 15rpx;
  transition: transform 0.3s ease;
  cursor: pointer;
}

.collapse-icon.expanded {
  transform: rotate(180deg);
}

.collapse-icon .iconfont {
  font-size: 24rpx;
  color: #b9c8dc;
}

.tech-card {
  background: rgba(18, 34, 46, 0.7);
  border: 1px solid rgba(33, 148, 255, 0.2);
  border-radius: 8px;
  margin-bottom: 20rpx;
  padding: 20rpx;
  position: relative;
  transition: all 0.3s ease;
}

.tech-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(33, 148, 255, 0.1);
}

.tech-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(33, 148, 255, 0.5),
    transparent
  );
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.status-indicator {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  position: relative;
}

.status-indicator::after {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  animation: statusPulse 2s infinite;
}

.status-indicator.error {
  background: #ff3b30;
}

.status-indicator.warning {
  background: #ff9500;
}

.status-indicator.success {
  background: #34c759;
}

.status-indicator.normal {
  background: #2194ff;
}

.status-indicator.up {
  background: #ff3b30;
}

.status-indicator.down {
  background: #34c759;
}

.status-indicator.stable {
  background: #2194ff;
}

@keyframes statusPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(33, 148, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(33, 148, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(33, 148, 255, 0);
  }
}

.analysis-title {
  font-size: 28rpx;
  color: #f5f5f5;
  font-weight: 500;
}

.header-right {
  display: flex;
  align-items: center;
}

.diagnosis-status {
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.diagnosis-status.error {
  background: rgba(255, 59, 48, 0.1);
  border: 1px solid rgba(255, 59, 48, 0.3);
  color: #ff3b30;
}

.diagnosis-status.warning {
  background: rgba(255, 149, 0, 0.1);
  border: 1px solid rgba(255, 149, 0, 0.3);
  color: #ff9500;
}

.diagnosis-status.success {
  background: rgba(52, 199, 89, 0.1);
  border: 1px solid rgba(52, 199, 89, 0.3);
  color: #34c759;
}

.diagnosis-status.normal {
  background: rgba(33, 148, 255, 0.1);
  border: 1px solid rgba(33, 148, 255, 0.3);
  color: #2194ff;
}

.card-content {
  padding: 10rpx 0;
}

.metrics-grid {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: rgba(27, 34, 51, 0.6);
  border-radius: 8rpx;
  border: 1px solid rgba(185, 200, 220, 0.1);
}

.metric-item {
  text-align: center;
  flex: 1;
}

.metric-label {
  font-size: 22rpx;
  color: #b9c8dc;
  margin-bottom: 10rpx;
}

.metric-value {
  font-size: 28rpx;
  color: #f5f5f5;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.metric-value.error {
  color: #ff3b30;
}

.metric-value.warning {
  color: #ff9500;
}

.metric-value.success {
  color: #34c759;
}

.unit {
  font-size: 20rpx;
  color: #b9c8dc;
}


.report-section {
  margin-top: 30rpx;
}

.report-tabs {
  display: flex;
  border-bottom: 1px solid rgba(185, 200, 220, 0.1);
  margin-bottom: 20rpx;
}

.report-tab {
  padding: 15rpx 25rpx;
  font-size: 26rpx;
  color: #b9c8dc;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
}

.report-tab.active {
  color: #2194ff;
}

.report-tab.active::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #2194ff, #00d4ff);
}

.report-content {
  min-height: 120rpx;
}

.report-list {
  padding: 20rpx;
  background: rgba(27, 34, 51, 0.4);
  border-radius: 8rpx;
  border: 1px solid rgba(185, 200, 220, 0.1);
}

.report-item {
  font-size: 26rpx;
  color: #b9c8dc;
  line-height: 1.6;
  margin-bottom: 15rpx;
  padding-left: 20rpx;
  position: relative;
}

.report-item::before {
  content: "•";
  position: absolute;
  left: 0;
  color: #2194ff;
  font-weight: bold;
}

.report-item:last-child {
  margin-bottom: 0;
}


.chart-section {
  margin-top: 30rpx;
  padding: 20rpx;
  background: rgba(27, 34, 51, 0.4);
  border-radius: 8rpx;
  border: 1px solid rgba(185, 200, 220, 0.1);
}

.chart-header {
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1px solid rgba(185, 200, 220, 0.1);
}

.chart-title {
  font-size: 28rpx;
  color: #f5f5f5;
  font-weight: 500;
}

.chart-container {
  width: 100%;
  height: 400rpx;
}


.indicators-section {
  padding: 0 20rpx;
}

.indicator-group {
  margin-bottom: 40rpx;
}

.indicator-content {
  overflow: hidden;
  transition: all 0.3s ease;
}

.diagnosis-content {
  overflow: hidden;
  transition: all 0.3s ease;
}

.indicator-card {
  background: rgba(18, 34, 46, 0.7);
  border: 1px solid rgba(33, 148, 255, 0.2);
  border-radius: 8px;
  margin-bottom: 20rpx;
  padding: 20rpx;
  position: relative;
  transition: all 0.3s ease;
}

.indicator-type {
  font-size: 28rpx;
  color: #f5f5f5;
  font-weight: 500;
}

.trend-indicator {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.trend-icon {
  width: 24rpx;
  height: 24rpx;
}

.trend-text {
  font-size: 22rpx;
  color: #b9c8dc;
}

.trend-indicator.trend-up .trend-text {
  color: #ff3b30;
}

.trend-indicator.trend-down .trend-text {
  color: #34c759;
}

.indicator-metrics {
  padding: 10rpx 0;
}

.metric-row {
  display: flex;
  gap: 30rpx;
  margin-bottom: 30rpx;
}

.metric-row .metric-item {
  flex: 1;
  text-align: left;
  padding: 20rpx;
  background: rgba(27, 34, 51, 0.6);
  border-radius: 8rpx;
  border: 1px solid rgba(185, 200, 220, 0.1);
}

.current-value {
  color: #2194ff;
}

.suggested-value {
  color: #00ff88;
}

.conclusion-section {
  padding: 20rpx;
  background: rgba(27, 34, 51, 0.4);
  border-radius: 8rpx;
  border: 1px solid rgba(185, 200, 220, 0.1);
}

.conclusion-label {
  font-size: 24rpx;
  color: #b9c8dc;
  margin-bottom: 15rpx;
}

.conclusion-content {
  font-size: 26rpx;
  color: #f5f5f5;
  line-height: 1.6;
}
</style>
<style scoped>
::v-deep .u-cell__title-text {
  color: #fff !important;
}

::v-deep .u-cell--clickable {
  background-color: #12222e !important;

}
</style>