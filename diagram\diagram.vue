<template>
	<view class="container">
		<AppHeader flex title="[CIMS]图表" />
		
		<!-- 工业风格顶部控制栏 -->
		<view class="control-panel">
			<!-- 时间选择器 -->
			<view class="time-selector" @click="openCalendar">
				<text class="selector-text">{{ currentDateRange }}</text>
				<text class="calendar-icon">📅</text>
			</view>
			
			<!-- 点位选择器 -->
			<view class="point-selector" @click="openPointSelector">
				<text class="selector-text">{{ currentPoint || '选择点位' }}</text>
				<text class="selector-icon">🔍</text>
			</view>
		</view>
		
		<!-- 时间范围快速选择 -->
		<view v-if="calendarShow" class="time-range-modal">
			<view class="modal-content">
				<view class="modal-header">
					<text class="modal-title">选择时间范围</text>
					<text class="modal-close" @click="calendarClose">✕</text>
				</view>
				
				<!-- 快速选择按钮 -->
				<view class="quick-select">
					<text class="quick-title">快速选择:</text>
					<view class="quick-buttons">
						<text class="quick-btn" @click="selectTimeRange('today')">今天</text>
						<text class="quick-btn" @click="selectTimeRange('yesterday')">昨天</text>
						<text class="quick-btn" @click="selectTimeRange('last7days')">最近7天</text>
						<text class="quick-btn" @click="selectTimeRange('last30days')">最近30天</text>
					</view>
				</view>
				
				<!-- 自定义时间选择 -->
				<view class="custom-select">
					<text class="custom-title">自定义时间:</text>
					<view class="date-inputs">
						<view class="date-input-group">
							<text class="input-label">开始日期:</text>
							<view class="date-input" @click="openStartDatePicker">
								<text class="date-text">{{ startDateDisplay }}</text>
								<text class="date-icon">📅</text>
							</view>
						</view>
						<view class="date-input-group">
							<text class="input-label">结束日期:</text>
							<view class="date-input" @click="openEndDatePicker">
								<text class="date-text">{{ endDateDisplay }}</text>
								<text class="date-icon">📅</text>
							</view>
						</view>
					</view>
					<view class="action-buttons">
						<text class="action-btn confirm-btn" @click="confirmCustomRange">确认</text>
						<text class="action-btn cancel-btn" @click="calendarClose">取消</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 点位选择弹窗 -->
		<view v-if="pointSelectorShow" class="point-selector-modal">
			<view class="modal-content">
				<view class="modal-header">
					<text class="modal-title">选择监测点位</text>
					<text class="modal-close" @click="closePointSelector">✕</text>
				</view>

				<!-- 机组选择 -->
				<view class="group-selector">
					<text class="group-title">选择机组:</text>
					<view class="group-buttons">
						<text
							v-for="group in allGroups"
							:key="group.id"
							class="group-btn"
							:class="{ active: currentGroup && currentGroup.id === group.id }"
							@click="selectGroup(group)"
						>
							{{ group.name }}
						</text>
					</view>
				</view>

				<!-- 点位选择 -->
				<view v-if="currentGroup" class="points-grid">
					<view
						v-for="(block, index) in selectedBlocks"
						:key="index"
						class="point-item"
						:class="{ active: index === activeBlockIndex }"
						@click="selectBlock(block, index)"
					>
						<view class="point-content">
							<text class="point-name">{{ block.label }}</text>
							<view class="point-indicator" :class="{ active: index === activeBlockIndex }"></view>
						</view>
					</view>
				</view>

				<view v-else class="no-group-selected">
					<text>请先选择机组</text>
				</view>

				<view class="action-buttons">
					<text class="action-btn confirm-btn" @click="confirmPointSelection">确认</text>
					<text class="action-btn cancel-btn" @click="closePointSelector">取消</text>
				</view>
			</view>
		</view>
		
		<!-- 日期选择器 -->
		<u-picker 
			:show="startDatePickerShow" 
			:columns="startDateColumns" 
			@confirm="confirmStartDate" 
			@cancel="startDatePickerShow = false"
			:mask-style="{ opacity: 0 }"
		></u-picker>
		
		<u-picker 
			:show="endDatePickerShow" 
			:columns="endDateColumns" 
			@confirm="confirmEndDate" 
			@cancel="endDatePickerShow = false"
			:mask-style="{ opacity: 0 }"
		></u-picker>
		
		<!-- 图表区域 -->
		<scroll-view class="scroll-content" scroll-y="true">
			<!-- 空数据组件 -->
			<EmptyData
				:show="!currentPoint"
				title="请选择监测点位"
				description="选择监测点位后即可查看相关图表数据"
			/>

			<view v-if="currentPoint && currentPoint !== '请选择点位'" class="charts-container">
				<!-- 第一个图表 -->
				<view class="chart-item">
					<view class="chart-header">
						<view class="chart-title">{{ calltendency.chartName || '图表1' }}</view>
						<view class="chart-unit">{{ calltendency.chartUnit || '' }}</view>
					</view>
					<view class="chart-wrapper">
						<SimpleChart 
							v-if="calltendency.xdata && calltendency.xdata.length > 0"
							:chart-data="{
								xdata: calltendency.xdata,
								series: calltendency.series,
								title: calltendency.title,
								legend: calltendency.legend,
								showLegend: true
							}"
						/>
						<view v-else class="chart-loading">
							<text>请选择点位查看数据</text>
						</view>
					</view>
					<!-- 统计信息 -->
					<view v-if="calltendency.stats" class="chart-stats">
						<view class="stats-item">
							<text class="stats-label">平均值:</text>
							<text class="stats-value">{{ calltendency.stats.avg }}{{ calltendency.chartUnit || '' }}</text>
						</view>
						<view class="stats-item">
							<text class="stats-label">最大值:</text>
							<text class="stats-value">{{ calltendency.stats.max }}{{ calltendency.chartUnit || '' }}</text>
						</view>
						<view class="stats-item">
							<text class="stats-label">最小值:</text>
							<text class="stats-value">{{ calltendency.stats.min }}{{ calltendency.chartUnit || '' }}</text>
						</view>
					</view>
				</view>
				
				<!-- 第二个图表 -->
				<view class="chart-item">
					<view class="chart-header">
						<view class="chart-title">{{ calltendency1.chartName || '图表2' }}</view>
						<view class="chart-unit">{{ calltendency1.chartUnit || '' }}</view>
					</view>
					<view class="chart-wrapper">
						<SimpleChart 
							v-if="calltendency1.xdata && calltendency1.xdata.length > 0"
							:chart-data="{
								xdata: calltendency1.xdata,
								series: calltendency1.series,
								title: calltendency1.title,
								legend: calltendency1.legend,
								showLegend: true
							}"
						/>
						<view v-else class="chart-loading">
							<text>请选择点位查看数据</text>
						</view>
					</view>
					<!-- 统计信息 -->
					<view v-if="calltendency1.stats" class="chart-stats">
						<view class="stats-item">
							<text class="stats-label">平均值:</text>
							<text class="stats-value">{{ calltendency1.stats.avg }}{{ calltendency1.chartUnit || '' }}</text>
						</view>
						<view class="stats-item">
							<text class="stats-label">最大值:</text>
							<text class="stats-value">{{ calltendency1.stats.max }}{{ calltendency1.chartUnit || '' }}</text>
						</view>
						<view class="stats-item">
							<text class="stats-label">最小值:</text>
							<text class="stats-value">{{ calltendency1.stats.min }}{{ calltendency1.chartUnit || '' }}</text>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>

	import {
		diagramConfiglable,diagramConfigEchartsWechat
	} from '@/api/diagram/diagram.js';
	import {
		getProject
	} from "@/utils/auth.js";
	import SimpleChart from '@/components/echarts/SimpleChart.vue';
	import EmptyData from '@/components/EmptyData/EmptyData.vue';

	export default {
		components: {
			SimpleChart,
			EmptyData
		},
		data() {
			return {
				iconColor: '#f5f5f5',
				iconShow: true,
				
				// 图表数据
				calltendency: {
					xdata: [],
					title: '',
					series: [],
					legend: ['同比', '环比', '实际值'],
					chartName: '',
					chartUnit: ''
				},
				calltendency1: {
					xdata: [],
					title: '',
					series: [],
					legend: ['同比', '环比', '实际值'],
					chartName: '',
					chartUnit: ''
				},
				
				// 时间选择相关
				calendarShow: false,
				currentDateRange: '今天',  // 默认显示今天
				selectedTimeRange: {
					startTime: '',
					endTime: ''
				},
				
				// 日期选择器相关
				startDatePickerShow: false,
				endDatePickerShow: false,
				startDateDisplay: '请选择开始日期',
				endDateDisplay: '请选择结束日期',
				tempStartDate: '',
				tempEndDate: '',
				startDateColumns: [],
				endDateColumns: [],
				
				// 点位选择相关
				pointSelectorShow: false,
				allGroups: [], // 所有机组数据
				selectedBlocks: [], // 当前显示的点位列表
				activeBlockIndex: -1,
				currentPoint: '请选择点位',
				currentGroup: null // 当前选中的机组
			};
		},
		methods: {
			// 打开日历
			openCalendar() {
				this.calendarShow = true;
				this.initDatePickers();
			},
			
			// 关闭日历
			calendarClose() {
				this.calendarShow = false;
				this.startDatePickerShow = false;
				this.endDatePickerShow = false;
			},
			
			// 初始化日期选择器数据
			initDatePickers() {
				const today = new Date();
				const currentYear = today.getFullYear();
				
				// 生成年份选项（过去2年到今年）
				const years = [];
				for (let year = currentYear - 2; year <= currentYear; year++) {
					years.push(year.toString());
				}
				
				// 生成月份选项
				const months = [];
				for (let month = 1; month <= 12; month++) {
					months.push(month.toString().padStart(2, '0'));
				}
				
				// 生成日期选项
				const days = [];
				for (let day = 1; day <= 31; day++) {
					days.push(day.toString().padStart(2, '0'));
				}
				
				this.startDateColumns = [years, months, days];
				this.endDateColumns = [years, months, days];
			},
			
			// 快速选择时间范围
			selectTimeRange(type) {
				const now = new Date();
				let startTime, endTime, displayText;
				
				switch (type) {
					case 'today':
						const today = this.formatDate(now);
						startTime = `${today} 00:00:00`;
						endTime = `${today} 23:59:59`;
						displayText = '今天';
						break;
					case 'yesterday':
						const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
						const yesterdayStr = this.formatDate(yesterday);
						startTime = `${yesterdayStr} 00:00:00`;
						endTime = `${yesterdayStr} 23:59:59`;
						displayText = '昨天';
						break;
					case 'last7days':
						const last7days = new Date(now.getTime() - 6 * 24 * 60 * 60 * 1000);
						startTime = `${this.formatDate(last7days)} 00:00:00`;
						endTime = `${this.formatDate(now)} 23:59:59`;
						displayText = '最近7天';
						break;
					case 'last30days':
						const last30days = new Date(now.getTime() - 29 * 24 * 60 * 60 * 1000);
						startTime = `${this.formatDate(last30days)} 00:00:00`;
						endTime = `${this.formatDate(now)} 23:59:59`;
						displayText = '最近30天';
						break;
				}
				
				this.selectedTimeRange = { startTime, endTime };
				this.currentDateRange = displayText;
				
				// 不立即关闭弹窗，等用户点击确认
				this.tempStartDate = startTime.split(' ')[0];
				this.tempEndDate = endTime.split(' ')[0];
				this.startDateDisplay = this.tempStartDate;
				this.endDateDisplay = this.tempEndDate;
			},
			
			// 打开开始日期选择器
			openStartDatePicker() {
				this.startDatePickerShow = true;
			},
			
			// 打开结束日期选择器
			openEndDatePicker() {
				this.endDatePickerShow = true;
			},
			
			// 确认开始日期
			confirmStartDate(e) {
				const values = e.value;
				const dateStr = `${values[0]}-${values[1]}-${values[2]}`;
				this.tempStartDate = dateStr;
				this.startDateDisplay = dateStr;
				this.startDatePickerShow = false;
			},
			
			// 确认结束日期
			confirmEndDate(e) {
				const values = e.value;
				const dateStr = `${values[0]}-${values[1]}-${values[2]}`;
				this.tempEndDate = dateStr;
				this.endDateDisplay = dateStr;
				this.endDatePickerShow = false;
			},
			
			// 确认自定义时间范围
			confirmCustomRange() {
				if (!this.tempStartDate || !this.tempEndDate) {
					uni.showToast({
						title: '请选择完整的时间范围',
						icon: 'none'
					});
					return;
				}
				
				const startDate = new Date(this.tempStartDate);
				const endDate = new Date(this.tempEndDate);
				
				if (startDate > endDate) {
					uni.showToast({
						title: '开始日期不能晚于结束日期',
						icon: 'none'
					});
					return;
				}
				
				this.selectedTimeRange = {
					startTime: `${this.tempStartDate} 00:00:00`,
					endTime: `${this.tempEndDate} 23:59:59`
				};
				
				if (this.tempStartDate === this.tempEndDate) {
					this.currentDateRange = this.tempStartDate;
				} else {
					this.currentDateRange = `${this.tempStartDate} ~ ${this.tempEndDate}`;
				}
				
				this.calendarClose();
				this.loadChartData();
			},
			
			// 点位选择相关方法
			openPointSelector() {
				this.pointSelectorShow = true;
			},
			
			closePointSelector() {
				this.pointSelectorShow = false;
			},
			
			// 选择机组
			selectGroup(group) {
				console.log('选择机组:', group);
				this.currentGroup = group;
				this.selectedBlocks = group.models || [];
				this.activeBlockIndex = -1; // 重置点位选择
				console.log('机组选择结果:', {
					currentGroup: this.currentGroup,
					selectedBlocks: this.selectedBlocks
				});
			},

			// 选择点位
			selectBlock(block, index) {
				console.log('选择点位:', block, 'index:', index);
				this.activeBlockIndex = index;
				this.currentPoint = block.label;
				console.log('点位选择结果:', {
					activeBlockIndex: this.activeBlockIndex,
					currentPoint: this.currentPoint,
					selectedBlock: block
				});
			},
			
			confirmPointSelection() {
				console.log('确认点位选择:', {
					activeBlockIndex: this.activeBlockIndex,
					currentPoint: this.currentPoint,
					selectedBlock: this.selectedBlocks[this.activeBlockIndex]
				});

				if (this.activeBlockIndex !== -1) {
					this.closePointSelector();
					// 添加延迟确保弹窗关闭后再加载数据
					this.$nextTick(() => {
						this.loadChartData();
					});
				} else {
					uni.showToast({
						title: '请选择监测点位',
						icon: 'none'
					});
				}
			},
			
			// 加载图表数据
			loadChartData() {
				if (!this.selectedTimeRange.startTime || !this.selectedTimeRange.endTime) {
					console.warn('时间范围未选择');
					uni.showToast({
						title: '请先选择时间范围',
						icon: 'none'
					});
					return;
				}

				if (this.activeBlockIndex === -1) {
					console.warn('未选择点位');
					uni.showToast({
						title: '请先选择监测点位',
						icon: 'none'
					});
					return;
				}

				const block = this.selectedBlocks[this.activeBlockIndex];
				const { startTime, endTime } = this.selectedTimeRange;

				console.log('开始加载图表数据:', {
					block: block,
					timeRange: { startTime, endTime },
					activeBlockIndex: this.activeBlockIndex
				});

				// 清空之前的图表数据
				this.calltendency = {
					xdata: [],
					title: '',
					series: [],
					legend: ['同比', '环比', '实际值'],
					chartName: '',
					chartUnit: ''
				};
				this.calltendency1 = {
					xdata: [],
					title: '',
					series: [],
					legend: ['同比', '环比', '实际值'],
					chartName: '',
					chartUnit: ''
				};

				// 加载第一个图表数据 (item)
				const params1 = {
					data: {
						deviceId: block.item.deviceId,
						identifier: block.item.identifier,
						startTime,
						endTime,
						displayStats: true,
						displayGrowth: true,
						clientType: "wechat"
					}
				};

				// 加载第二个图表数据 (refer)
				const params2 = {
					data: {
						deviceId: block.refer.deviceId,
						identifier: block.refer.identifier,
						startTime,
						endTime,
						displayStats: true,
						displayGrowth: true,
						clientType: "wechat"
					}
				};

				console.log('API调用参数1:', params1);
				console.log('API调用参数2:', params2);

				this.loadFirstChart(params1, block.item);
				this.loadSecondChart(params2, block.refer);
			},
			
			// 加载第一个图表
			loadFirstChart(params, itemInfo) {
				console.log('开始加载第一个图表:', itemInfo.name);
				diagramConfigEchartsWechat(params).then(res => {
					console.log('第一个图表API响应:', res);
					if (res.code === 200 && res.data) {
						const data = res.data;
						console.log('第一个图表原始数据:', data);
						const chartData = this.processChartData(data);
						console.log('第一个图表处理后数据:', chartData);

						// 使用 $set 确保响应式更新
						this.$set(this, 'calltendency', {
							...chartData,
							legend: ['同比', '环比', '实际值'],
							chartName: data.name || itemInfo.name,
							chartUnit: data.unit || itemInfo.unit
						});

						console.log('第一个图表数据更新完成:', this.calltendency);
					} else {
						console.error('加载第一个图表失败:', res);
						uni.showToast({
							title: `${itemInfo.name}数据加载失败`,
							icon: 'none'
						});
					}
				}).catch(error => {
					console.error('加载第一个图表异常:', error);
					uni.showToast({
						title: `${itemInfo.name}数据加载异常`,
						icon: 'none'
					});
				});
			},

			// 加载第二个图表
			loadSecondChart(params, referInfo) {
				console.log('开始加载第二个图表:', referInfo.name);
				diagramConfigEchartsWechat(params).then(res => {
					console.log('第二个图表API响应:', res);
					if (res.code === 200 && res.data) {
						const data = res.data;
						console.log('第二个图表原始数据:', data);
						const chartData = this.processChartData(data);
						console.log('第二个图表处理后数据:', chartData);

						// 使用 $set 确保响应式更新
						this.$set(this, 'calltendency1', {
							...chartData,
							legend: ['同比', '环比', '实际值'],
							chartName: data.name || referInfo.name,
							chartUnit: data.unit || referInfo.unit
						});

						console.log('第二个图表数据更新完成:', this.calltendency1);
					} else {
						console.error('加载第二个图表失败:', res);
						uni.showToast({
							title: `${referInfo.name}数据加载失败`,
							icon: 'none'
						});
					}
				}).catch(error => {
					console.error('加载第二个图表异常:', error);
					uni.showToast({
						title: `${referInfo.name}数据加载异常`,
						icon: 'none'
					});
				});
			},

			// 判断时间范围是否超过一天
			isTimeRangeMultipleDays() {
				if (!this.selectedTimeRange.startTime || !this.selectedTimeRange.endTime) {
					return false;
				}

				const startDate = new Date(this.selectedTimeRange.startTime);
				const endDate = new Date(this.selectedTimeRange.endTime);

				// 计算时间差（毫秒）
				const timeDiff = endDate.getTime() - startDate.getTime();
				// 转换为天数
				const daysDiff = timeDiff / (1000 * 60 * 60 * 24);

				// 如果超过1天，返回true
				return daysDiff > 1;
			},

			// 处理图表数据
			processChartData(data) {
				console.log('开始处理图表数据:', data);

				// 检查数据结构
				if (!data || !data.his || !Array.isArray(data.his)) {
					console.error('数据格式错误，缺少his数组:', data);
					return {
						xdata: [],
						title: '数据格式错误',
						series: [],
						stats: null
					};
				}

				if (data.his.length === 0) {
					console.warn('his数组为空，没有历史数据');
					return {
						xdata: [],
						title: '暂无数据',
						series: [],
						stats: null
					};
				}

				// 判断时间范围是否超过一天
				const isMultipleDays = this.isTimeRangeMultipleDays();

				const xdata = data.his.map(item => {
					const date = new Date(item.time.replace(' ', 'T'));

					if (isMultipleDays) {
						// 超过一天：显示月-日格式
						const month = String(date.getMonth() + 1).padStart(2, '0');
						const day = String(date.getDate()).padStart(2, '0');
						return `${month}-${day}`;
					} else {
						// 一天内：显示小时:分钟格式
						const hours = String(date.getHours()).padStart(2, '0');
						const minutes = String(date.getMinutes()).padStart(2, '0');
						return `${hours}:${minutes}`;
					}
				});

				const series = [
					{
						name: '同比',
						data: data.his.map(item => item.tbValue)
					},
					{
						name: '环比',
						data: data.his.map(item => item.hbValue)
					},
					{
						name: `${data.name}${data.unit || ''}`,
						data: data.his.map(item => item.value)
					}
				];

				const processedData = {
					xdata,
					title: this.getChartTitle(data),
					series,
					stats: {
						max: data.max,
						min: data.min,
						avg: data.avg
					}
				};

				console.log('图表数据处理完成:', processedData);
				return processedData;
			},
			
			// 获取图表标题
			getChartTitle(data) {
				if (data.max !== null && data.min !== null && data.avg !== null) {
					return `最大值:${data.max} 最小值:${data.min} 平均值:${data.avg}`;
				}
				return '暂无数据';
			},
			
			// 格式化日期
			formatDate(date) {
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				return `${year}-${month}-${day}`;
			},
			
			// 初始化数据
			async initData() {
				try {
					const project = getProject();
					const params = {
						data: {
							configType: 3,
							projectId: project.id,
						}
					};

					console.log('初始化数据，调用diagramConfiglable接口...');
					const res = await diagramConfiglable(params);
					console.log('diagramConfiglable接口响应:', res);

					if (res.data && res.data.length > 0) {
						// 保存所有机组数据
						this.allGroups = res.data;
						console.log('所有机组数据:', this.allGroups);

						// 默认选中第一个机组
						this.currentGroup = this.allGroups[0];
						this.selectedBlocks = this.currentGroup.models || [];

						// 默认选中第一个点位
						if (this.selectedBlocks.length > 0) {
							this.activeBlockIndex = 0;
							this.currentPoint = this.selectedBlocks[0].label;
						}

						console.log('初始化选择结果:', {
							currentGroup: this.currentGroup,
							selectedBlocks: this.selectedBlocks,
							activeBlockIndex: this.activeBlockIndex,
							currentPoint: this.currentPoint
						});

						// 默认选择今天
						const now = new Date();
						const today = this.formatDate(now);
						this.selectedTimeRange = {
							startTime: `${today} 00:00:00`,
							endTime: `${today} 23:59:59`
						};
						this.currentDateRange = '今天';
						this.tempStartDate = today;
						this.tempEndDate = today;
						this.startDateDisplay = today;
						this.endDateDisplay = today;

						// 加载图表数据
						this.loadChartData();
					} else {
						console.warn('没有获取到机组数据');
						uni.showToast({
							title: '没有可用的机组数据',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('初始化数据失败:', error);
					uni.showToast({
						title: '初始化数据失败',
						icon: 'none'
					});
				}
			}
		},
		mounted() {
			this.initData();
		}
	};
</script>

<style scoped>
	.container {
		height: 100vh;
		background: linear-gradient(135deg, #0a0f1c 0%, #1a1f2e 50%, #0f1419 100%);
		overflow: hidden;
	}

	/* 控制面板 */
	.control-panel {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx;
		background: rgba(33, 148, 255, 0.1);
		border-bottom: 1px solid rgba(33, 148, 255, 0.3);
	}

	.time-selector,
	.point-selector {
		display: flex;
		align-items: center;
		justify-content: space-between;
		background: rgba(33, 148, 255, 0.15);
		border: 1px solid rgba(33, 148, 255, 0.4);
		border-radius: 12rpx;
		padding: 16rpx 24rpx;
		min-width: 280rpx;
	}

	.selector-text {
		font-size: 26rpx;
		font-weight: 500;
		color: #fff;
	}

	.calendar-icon,
	.selector-icon {
		font-size: 32rpx;
		margin-left: 16rpx;
		color: rgba(255, 255, 255, 0.7);
	}

	/* 时间范围选择器 */
	.time-range-modal,
	.point-selector-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.8);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
	}

	.modal-content {
		background: linear-gradient(135deg, #1a1f2e 0%, #2a2f3e 100%);
		border: 1px solid rgba(33, 148, 255, 0.3);
		border-radius: 20rpx;
		width: 640rpx;
		max-height: 80vh;
		overflow-y: auto;
		box-shadow: 0 0 30rpx rgba(33, 148, 255, 0.3);
	}

	.modal-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx 40rpx;
		border-bottom: 1px solid rgba(33, 148, 255, 0.3);
		background: rgba(33, 148, 255, 0.1);
	}

	.modal-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #fff;
		text-shadow: 0 0 8rpx rgba(33, 148, 255, 0.5);
	}

	.modal-close {
		font-size: 48rpx;
		color: rgba(255, 255, 255, 0.7);
		font-weight: bold;
		padding: 10rpx;
		line-height: 1;
	}

	/* 快速选择区域 */
	.quick-select {
		padding: 30rpx 40rpx;
		border-bottom: 1px solid rgba(33, 148, 255, 0.2);
	}

	.quick-title {
		font-size: 28rpx;
		color: #fff;
		margin-bottom: 20rpx;
		display: block;
	}

	.quick-buttons {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
	}

	.quick-btn {
		background: linear-gradient(135deg, rgba(33, 148, 255, 0.2) 0%, rgba(0, 255, 136, 0.1) 100%);
		border: 1px solid rgba(33, 148, 255, 0.4);
		color: #fff;
		padding: 20rpx 30rpx;
		border-radius: 12rpx;
		font-size: 26rpx;
		min-width: 120rpx;
		text-align: center;
		transition: all 0.3s ease;
	}

	.quick-btn:active {
		background: linear-gradient(135deg, rgba(33, 148, 255, 0.4) 0%, rgba(0, 255, 136, 0.2) 100%);
		transform: scale(0.95);
	}

	/* 自定义选择区域 */
	.custom-select {
		padding: 30rpx 40rpx;
	}

	.custom-title {
		font-size: 28rpx;
		color: #fff;
		margin-bottom: 20rpx;
		display: block;
	}

	.date-inputs {
		margin-bottom: 30rpx;
	}

	.date-input-group {
		margin-bottom: 20rpx;
	}

	.input-label {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.7);
		margin-bottom: 10rpx;
		display: block;
	}

	.date-input {
		display: flex;
		align-items: center;
		justify-content: space-between;
		background: rgba(33, 148, 255, 0.1);
		border: 1px solid rgba(33, 148, 255, 0.3);
		border-radius: 12rpx;
		padding: 20rpx 24rpx;
		min-height: 80rpx;
	}

	.date-text {
		font-size: 28rpx;
		color: #fff;
		flex: 1;
	}

	.date-icon {
		font-size: 32rpx;
		color: rgba(255, 255, 255, 0.7);
	}

	/* 机组选择区域 */
	.group-selector {
		padding: 30rpx 40rpx;
		border-bottom: 1px solid rgba(33, 148, 255, 0.2);
	}

	.group-title {
		font-size: 28rpx;
		color: #fff;
		margin-bottom: 20rpx;
		display: block;
	}

	.group-buttons {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
	}

	.group-btn {
		background: linear-gradient(135deg, rgba(33, 148, 255, 0.2) 0%, rgba(0, 255, 136, 0.1) 100%);
		border: 1px solid rgba(33, 148, 255, 0.4);
		color: #fff;
		padding: 20rpx 30rpx;
		border-radius: 12rpx;
		font-size: 26rpx;
		min-width: 120rpx;
		text-align: center;
		transition: all 0.3s ease;
	}

	.group-btn.active {
		background: linear-gradient(135deg, rgba(33, 148, 255, 0.4) 0%, rgba(0, 255, 136, 0.2) 100%);
		border-color: rgba(33, 148, 255, 0.6);
		transform: scale(1.02);
	}

	.group-btn:active {
		transform: scale(0.95);
	}

	/* 无机组选择提示 */
	.no-group-selected {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 60rpx 40rpx;
		color: rgba(255, 255, 255, 0.6);
		font-size: 28rpx;
	}

	/* 点位选择网格 */
	.points-grid {
		display: grid;
		grid-template-columns: repeat(auto-fill, minmax(140rpx, 1fr));
		gap: 20rpx;
		padding: 30rpx 40rpx;
	}

	.point-item {
		background: rgba(33, 148, 255, 0.1);
		border: 1px solid rgba(33, 148, 255, 0.3);
		border-radius: 12rpx;
		padding: 20rpx;
		transition: all 0.3s ease;
	}

	.point-item.active {
		background: linear-gradient(135deg, rgba(33, 148, 255, 0.3) 0%, rgba(0, 255, 136, 0.15) 100%);
		border-color: rgba(33, 148, 255, 0.6);
		transform: scale(1.02);
	}

	.point-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 10rpx;
	}

	.point-name {
		font-size: 24rpx;
		color: #fff;
		text-align: center;
	}

	.point-indicator {
		width: 20rpx;
		height: 2rpx;
		background: rgba(33, 148, 255, 0.3);
		border-radius: 1rpx;
		transition: all 0.3s ease;
	}

	.point-indicator.active {
		background: linear-gradient(90deg, #2194FF, #00ff88);
		box-shadow: 0 0 10rpx rgba(33, 148, 255, 0.5);
	}

	/* 操作按钮 */
	.action-buttons {
		display: flex;
		gap: 20rpx;
		justify-content: flex-end;
		padding: 20rpx 40rpx;
		border-top: 1px solid rgba(33, 148, 255, 0.2);
	}

	.action-btn {
		padding: 20rpx 40rpx;
		border-radius: 12rpx;
		font-size: 28rpx;
		text-align: center;
		min-width: 120rpx;
		font-weight: bold;
	}

	.confirm-btn {
		background: linear-gradient(135deg, #2194FF 0%, #00ff88 100%);
		color: #fff;
		box-shadow: 0 0 20rpx rgba(33, 148, 255, 0.5);
	}

	.cancel-btn {
		background: rgba(255, 255, 255, 0.1);
		border: 1px solid rgba(255, 255, 255, 0.3);
		color: #fff;
	}

	/* 图表区域 */
	.scroll-content {
		height: calc(100vh - 180rpx);
		overflow-y: auto;
	}

	.charts-container {
		padding: 20rpx;
	}

	.chart-item {
		background: linear-gradient(135deg, rgba(33, 148, 255, 0.1) 0%, rgba(0, 255, 136, 0.05) 100%);
		border: 1px solid rgba(33, 148, 255, 0.3);
		border-radius: 16rpx;
		margin-bottom: 30rpx;
		overflow: hidden;
	}

	.chart-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx 28rpx;
		background: rgba(33, 148, 255, 0.15);
		border-bottom: 1px solid rgba(33, 148, 255, 0.3);
	}

	.chart-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #fff;
		text-shadow: 0 0 8rpx rgba(33, 148, 255, 0.5);
	}

	.chart-unit {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.7);
		background: rgba(0, 255, 136, 0.2);
		padding: 6rpx 12rpx;
		border-radius: 20rpx;
		border: 1px solid rgba(0, 255, 136, 0.3);
	}

	.chart-wrapper {
		height: 400rpx;
		position: relative;
		background: rgba(0, 0, 0, 0.2);
	}

	.chart-loading {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 100%;
		color: rgba(255, 255, 255, 0.6);
		font-size: 28rpx;
	}

	/* 统计信息 */
	.chart-stats {
		display: flex;
		justify-content: space-around;
		padding: 20rpx 28rpx;
		background: rgba(0, 0, 0, 0.3);
		border-top: 1px solid rgba(33, 148, 255, 0.3);
	}

	.stats-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 8rpx;
	}

	.stats-label {
		font-size: 22rpx;
		color: rgba(255, 255, 255, 0.6);
	}

	.stats-value {
		font-size: 26rpx;
		font-weight: bold;
		color: #00ff88;
		font-family: 'Courier New', monospace;
	}
</style>