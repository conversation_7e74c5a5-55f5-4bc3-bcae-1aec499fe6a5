<template>
	<view class="container">
		<AppHeader flex title="[CIMS]数据监控" />
		
		<!-- 工业风格标签页 -->
		<view class="tabs-container">
			<view class="tabs-header">
				<view class="section-title">
					<text class="title-text">设备监控</text>
					<view class="title-decoration">
						<view class="deco-line"></view>
						<view class="deco-dot"></view>
					</view>
				</view>
				<view class="data-summary">
					<text class="summary-text">{{ dataMonitorContent.length }}项数据</text>
				</view>
			</view>
			
			<view class="tabs-wrapper">
				<scroll-view class="tabs-scroll" scroll-x="true">
					<view class="tab-list">
						<view 
							v-for="tab in tabList" 
							:key="tab.id"
							class="tab-item"
							:class="{ active: selectedTab && selectedTab.id === tab.id }"
							@click="handleTabClick(tab)"
						>
							<view class="tab-content">
								<view class="tab-indicator"></view>
								<text class="tab-name">{{ tab.name }}</text>
							</view>
							<view class="tab-border"></view>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>
		
		<!-- 数据展示区域 -->
		<scroll-view class="scroll-content" scroll-y="true">
		<view class="content">
				<!-- 数据概览面板 -->
				<view v-if="dataMonitorContent.length > 0" class="overview-panel">
					<view class="overview-header">
						<text class="overview-title">实时数据概览</text>
						<view class="scanning-line"></view>
					</view>
					<view class="overview-stats">
						<view class="stat-item">
							<text class="stat-label">监控点位</text>
							<text class="stat-value">{{ dataMonitorContent.length }}</text>
						</view>
						<view class="stat-item">
							<text class="stat-label">数据源</text>
							<text class="stat-value">{{ selectedTab ? selectedTab.name : '--' }}</text>
						</view>
						<view class="stat-item">
							<text class="stat-label">更新时间</text>
							<text class="stat-value">{{ lastUpdateTime }}</text>
						</view>
					</view>
				</view>
				
				<!-- 数据卡片列表 -->
				<view v-if="dataMonitorContent.length > 0" class="data-grid">
					<view 
						v-for="(data, index) in dataMonitorContent" 
						:key="index"
						class="data-card"
						:style="{ 'animation-delay': (index * 0.1) + 's' }"
					>
						<view class="card-frame">
							<view class="frame-corner top-left"></view>
							<view class="frame-corner top-right"></view>
							<view class="frame-corner bottom-left"></view>
							<view class="frame-corner bottom-right"></view>
						</view>
						
						<view class="card-content">
							<view class="data-info">
								<view class="data-name">{{ data.name || '--' }}</view>
								<view class="data-time">{{ data.time || '--' }}</view>
							</view>
							<view class="data-value">
								<text class="value-number">{{ data.value || '--' }}</text>
								<text class="value-unit">{{ data.unit || '' }}</text>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 空数据组件 -->
				<EmptyData
					:show="dataMonitorContent.length === 0"
					title="暂无监控数据"
					description="请选择设备查看实时监控数据"
				/>
				
			<view class="content-bottom-placeholder"></view>
		</view>
		</scroll-view>
	</view>
</template>

<script>
	import {
		dataMonitorAll,
		pointData
	} from '@/api/dataMonitor/api.js'
	import {
		getProject
	} from "@/utils/auth.js";
	import EmptyData from '@/components/EmptyData/EmptyData.vue';

	export default {
		components: {
			EmptyData
		},
		data() {
			return {
				iconColor: '#f5f5f5',
				iconShow: true,
				tabList: [],
				selectedTab: null,
				inactiveStyle: {
					color: '#646566'
				},
				activeStyle: {
					color: '#f5f5f5'
				},
				dataMonitorContent: [],
				lastUpdateTime: ''
			}
		},
		onLoad() {
			this.getdataMonitorList()
		},
		methods: {
			// 更新时间
			updateTime() {
				const now = new Date();
				this.lastUpdateTime = now.toLocaleTimeString();
			},
			
			getpoinData(data) {
				const params = {
					data: {
						deviceId: data.deviceId,
						pointType: 0
					}
				}
				pointData(params)
					.then(res => {
						if (res.data.datas.length > 0) {
							this.dataMonitorContent = res.data.datas
							this.dataMonitorContent.map(item => {
								item.time = res.data.time
							})
						} else {
							this.dataMonitorContent = []
						}
						this.updateTime();
					})
			},
			
			handleTabClick(item) {
				console.log(item, '----');
				this.selectedTab = item;
				this.getpoinData(item)
			},
			
			getdataMonitorList() {
				const projectObj = getProject();
				const data = projectObj.id
				const params = {
					data
				}
				dataMonitorAll(params)
					.then(res => {
						this.tabList = res.data.map(item => {
						return {
								id: item.productId,
								name: item.productName,
								deviceId: item.deviceId
							}
					})
						if (this.tabList.length > 0) {
							this.selectedTab = this.tabList[0];
					this.getpoinData(this.tabList[0])
						}
				})
			}
		}
	}
</script>

<style scoped>
	.container {
		height: 100vh;
		background: linear-gradient(135deg, #0a0f1c 0%, #1a1f2e 50%, #0f1419 100%);
		position: relative;
		overflow: hidden;
	}

	.container::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: 
			radial-gradient(circle at 20% 20%, rgba(33, 148, 255, 0.1) 0%, transparent 50%),
			radial-gradient(circle at 80% 80%, rgba(0, 255, 136, 0.08) 0%, transparent 50%);
		pointer-events: none;
	}

	/* 标签页容器 */
	.tabs-container {
		background: rgba(33, 148, 255, 0.08);
		border-bottom: 1px solid rgba(33, 148, 255, 0.3);
		padding: 16rpx 0;
	}

	.tabs-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 24rpx 16rpx;
	}

	.section-title {
		display: flex;
		align-items: center;
		gap: 16rpx;
	}

	.title-text {
		font-size: 26rpx;
		font-weight: bold;
		color: #fff;
		text-shadow: 0 0 10rpx rgba(33, 148, 255, 0.5);
		white-space: nowrap;
	}

	.title-decoration {
		display: flex;
		align-items: center;
		gap: 8rpx;
	}

	.deco-line {
		width: 48rpx;
		height: 3rpx;
		background: linear-gradient(90deg, #2194FF, #00ff88);
		border-radius: 2rpx;
	}

	.deco-dot {
		width: 10rpx;
		height: 10rpx;
		background: #00ff88;
		border-radius: 50%;
		box-shadow: 0 0 8rpx rgba(0, 255, 136, 0.5);
	}

	.data-summary {
		padding: 8rpx 16rpx;
		background: rgba(0, 255, 136, 0.1);
		border: 1px solid rgba(0, 255, 136, 0.3);
		border-radius: 16rpx;
	}

	.summary-text {
		font-size: 18rpx;
		color: #00ff88;
		white-space: nowrap;
	}

	.tabs-wrapper {
		padding: 0 24rpx;
	}

	.tabs-scroll {
		white-space: nowrap;
		overflow-x: auto;
	}

	.tab-list {
		display: flex;
		gap: 12rpx;
	}

	.tab-item {
		 position: relative;
		min-width: 100rpx;
		max-width: 180rpx;
		padding: 10rpx 14rpx;
		background: rgba(33, 148, 255, 0.1);
		border: 1px solid rgba(33, 148, 255, 0.3);
		border-radius: 12rpx;
		transition: all 0.3s ease;
		  overflow: hidden;
		flex-shrink: 0;
	}

	.tab-item.active {
		background: linear-gradient(135deg, rgba(33, 148, 255, 0.2) 0%, rgba(0, 255, 136, 0.1) 100%);
		border-color: rgba(33, 148, 255, 0.5);
		transform: scale(1.02);
	}

	.tab-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 4rpx;
		width: 100%;
	}

	.tab-indicator {
		width: 20rpx;
		height: 2rpx;
		background: linear-gradient(90deg, #2194FF, #00ff88);
		border-radius: 1rpx;
		opacity: 0.3;
		transition: opacity 0.3s ease;
	}

	.tab-item.active .tab-indicator {
		opacity: 1;
	}

	.tab-name {
		font-size: 20rpx;
		font-weight: bold;
		color: #fff;
		text-align: center;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		max-width: 100%;
	}

	.tab-border {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		border-radius: 12rpx;
		background: linear-gradient(45deg, transparent, rgba(33, 148, 255, 0.3), transparent);
		opacity: 0;
		transition: opacity 0.3s ease;
	}

	.tab-item.active .tab-border {
		opacity: 1;
	}

	/* 滚动内容区域 */
	.scroll-content {
		height: calc(100vh - 200rpx);
		overflow-y: auto;
	}

	.content {
		padding: 24rpx;
		padding-bottom: 150rpx;
	}

	/* 概览面板 */
	.overview-panel {
		background: linear-gradient(135deg, rgba(33, 148, 255, 0.1) 0%, rgba(0, 255, 136, 0.05) 100%);
		border: 1px solid rgba(33, 148, 255, 0.3);
		border-radius: 16rpx;
		padding: 24rpx;
		margin-bottom: 24rpx;
		position: relative;
		overflow: hidden;
	}

	.overview-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16rpx;
	}

	.overview-title {
		font-size: 26rpx;
		font-weight: bold;
		color: #fff;
		text-shadow: 0 0 8rpx rgba(33, 148, 255, 0.5);
	}

	.scanning-line {
		width: 80rpx;
		height: 3rpx;
		background: linear-gradient(90deg, transparent, #00ff88, transparent);
		border-radius: 2rpx;
		animation: scan 2s infinite;
	}

	@keyframes scan {
		0% { transform: translateX(-80rpx); opacity: 0; }
		50% { opacity: 1; }
		100% { transform: translateX(80rpx); opacity: 0; }
	}

	.overview-stats {
		display: flex;
		justify-content: space-around;
		gap: 16rpx;
	}

	.stat-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 6rpx;
	}

	.stat-label {
		font-size: 18rpx;
		color: rgba(255, 255, 255, 0.6);
		white-space: nowrap;
	}

	.stat-value {
		font-size: 22rpx;
		font-weight: bold;
		color: #00ff88;
		font-family: 'Courier New', monospace;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		max-width: 120rpx;
	}

	/* 数据卡片网格 */
	.data-grid {
		display: flex;
		flex-direction: column;
		gap: 16rpx;
	}

	.data-card {
		position: relative;
		background: linear-gradient(135deg, rgba(33, 148, 255, 0.08) 0%, rgba(0, 255, 136, 0.03) 100%);
		border: 1px solid rgba(33, 148, 255, 0.3);
		border-radius: 16rpx;
		padding: 24rpx;
		margin-bottom: 8rpx;
		overflow: hidden;
		animation: slideInUp 0.6s ease forwards;
		opacity: 0;
	}

	@keyframes slideInUp {
		0% {
			transform: translateY(30rpx);
			opacity: 0;
		}
		100% {
			transform: translateY(0);
			opacity: 1;
		}
	}

	.card-frame {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		pointer-events: none;
	}

	.frame-corner {
		position: absolute;
		width: 16rpx;
		height: 16rpx;
		border: 2rpx solid rgba(33, 148, 255, 0.6);
	}

	.frame-corner.top-left {
		top: 10rpx;
		left: 10rpx;
		border-right: none;
		border-bottom: none;
	}

	.frame-corner.top-right {
		top: 10rpx;
		right: 10rpx;
		border-left: none;
		border-bottom: none;
	}

	.frame-corner.bottom-left {
		bottom: 10rpx;
		left: 10rpx;
		border-right: none;
		border-top: none;
	}

	.frame-corner.bottom-right {
		bottom: 10rpx;
		right: 10rpx;
		border-left: none;
		border-top: none;
	}

	.card-content {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		padding: 10rpx 0;
	}

	.data-info {
		flex: 1;
	}

	.data-name {
		font-size: 26rpx;
		font-weight: bold;
		color: #fff;
		margin-bottom: 6rpx;
		word-break: break-all;
		overflow-wrap: break-word;
	}

	.data-time {
		font-size: 20rpx;
		color: rgba(255, 255, 255, 0.6);
		font-family: 'Courier New', monospace;
	}

	.data-value {
		display: flex;
		align-items: baseline;
		gap: 8rpx;
		text-align: right;
	}

	.value-number {
		font-size: 30rpx;
		font-weight: bold;
		color: #2194FF;
		font-family: 'Courier New', monospace;
		text-shadow: 0 0 10rpx rgba(33, 148, 255, 0.5);
	}

	.value-unit {
		font-size: 20rpx;
		color: rgba(255, 255, 255, 0.7);
		font-weight: 500;
	}

	/* 空状态 */
	.empty-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 80rpx 0;
	}

	.empty-icon {
		font-size: 80rpx;
		margin-bottom: 24rpx;
		opacity: 0.5;
	}

	.empty-text {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.7);
		margin-bottom: 8rpx;
	}

	.empty-tip {
		font-size: 22rpx;
		color: rgba(255, 255, 255, 0.5);
	}

	.content-bottom-placeholder {
		height: 50rpx;
	}
</style>
