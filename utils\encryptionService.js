// src/utils/encryption.js
import CryptoJS from 'crypto-js';

const KEY = CryptoJS.enc.Utf8.parse("1234567890123456");
const IV = CryptoJS.enc.Utf8.parse('1234567890123456');

/**
 * AES加密 ：字符串 key iv  返回base64 
 */
export function Encrypt(word, keyStr, ivStr) {
  let key = KEY;
  let iv = IV;

  if (keyStr) {
    key = CryptoJS.enc.Utf8.parse(keyStr);
    iv = CryptoJS.enc.Utf8.parse(ivStr);
  }

  let srcs = CryptoJS.enc.Utf8.parse(word);
  var encrypted = CryptoJS.AES.encrypt(srcs, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.ZeroPadding
  });

  return CryptoJS.enc.Base64.stringify(encrypted.ciphertext);
}

/**
 * AES解密：传入 base64 加密后的字符串 key iv 返回解密后的明文
 */
export function Decrypt(word, keyStr, ivStr) {
  let key = KEY;
  let iv = IV;

  if (keyStr) {
    key = CryptoJS.enc.Utf8.parse(keyStr);
    iv = CryptoJS.enc.Utf8.parse(ivStr);
  }

  // 解析 base64 字符串，获取密文
  const ciphertext = CryptoJS.enc.Base64.parse(word);
  // 构造 CipherParams 对象
  const cipherParams = CryptoJS.lib.CipherParams.create({
    ciphertext: ciphertext
  });
  const decrypted = CryptoJS.AES.decrypt(cipherParams, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.ZeroPadding
  });

  return decrypted.toString(CryptoJS.enc.Utf8);
}
