<template>
  <view style="width: 100%; height: 100%; margin: 20rpx 0;">
    <qiun-data-charts 
      :type="chartType"
      :opts="chartOptions"
      :chartData="chartData"
      :ontouch="true"
      background="rgba(9, 20, 28, 1)"
	   canvas2d
    />
  </view>
</template>

<script>
export default {
  name: 'Common<PERSON>hart',
  props: {
    chartType: {
      type: String,
      default: 'column'
    },
    data: {
      type: Object,
      required: true
    }
  },
  watch: {
    data: {
      deep: true,
      handler(newVal) {
        if (newVal.xaxisData && newVal.yaxisData) {
          this.chartData.categories = newVal.xaxisData.map(timeStr => {
            const match = timeStr.match(/(\d{2}):(\d{2}):\d{2}/);
            if (match) {
              return `${match[1]}:${match[2]}`;
            }
            return timeStr; 
          });

          this.chartData.series = [
            {
              // 这里不再设置 name，图例将不会显示
              data: newVal.yaxisData
            }
          ];

          if (this.chartOptions.yAxis.data.length > 0) {
            this.chartOptions.yAxis.data[0].title = newVal.yaxisUnit || '';
          }
        } else {
          this.chartData.categories = [];
          this.chartData.series = [];
          if (this.chartOptions.yAxis.data.length > 0) {
            this.chartOptions.yAxis.data[0].title = '';
          }
          this.chartOptions.xAxis.itemCount = 5;
        }
      }
    }
  },
  data() {
    return {
      chartOptions: {
        fontColor: "#FFFFFF",
        fontSize: 10,
        color: ["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],
        touchMoveLimit: 25,
        padding: [20, 5, 5, 5],
        dataLabel: true,
        dataPointShape: false,
        legend: {
          position: "bottom",
          float: "bottom",
          show: false  // 隐藏图例
        },
        enableScroll: true,
        xAxis: {
          marginTop: 8,
          disableGrid: true,
          itemCount: 10,
          scrollShow: true,
          scrollBackgroundColor: '#082b42',
          scrollColor: '#064060',
          axisLineColor: '#0a214d',
          fontColor: '#999999',
          fontSize: 10,
          scrollAlign: 'right',
        },
        yAxis: {
          showTitle: true,
          gridType: "dash",
          dashLength: 2,
          fontColor: '#999999',
          fontSize: 10,
          showTitle: true,
          gridColor: "#064060",
          data: [{
            title: '',
            titleFontColor: "#999999",
          }]
        }
      },
      chartData: {
        categories: [],
        series: []
      }
    };
  },
  mounted() {
    this.$watch('data', this.$options.watch.data.handler, { immediate: true, deep: true });
  }
};
</script>

<style scoped>
/* 根据实际需求修改父元素尺寸，组件自动识别宽高 */
</style>
