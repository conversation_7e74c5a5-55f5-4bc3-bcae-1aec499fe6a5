<template>
	<view class="chart-container">
		<view v-if="hasData" class="chart-wrapper">
			<qiun-data-charts 
				type="line" 
				:opts="chartOptions" 
				:chartData="processedData"
				:ontouch="true"
				:canvas2d="true"
			/>
		</view>
		<view v-else class="no-data-wrapper">
			<text class="no-data-text">{{ emptyText }}</text>
		</view>
	</view>
</template>

<script>
export default {
	name: 'ChartView',
	props: {
		// 图表数据
		data: {
			type: Object,
			default: () => ({})
		},
		// 图表高度
		height: {
			type: Number,
			default: 300
		},
		// 空数据提示文本
		emptyText: {
			type: String,
			default: '暂无数据'
		}
	},
	computed: {
		hasData() {
			return this.data && this.data.xdata && this.data.xdata.length > 0;
		},
		
		processedData() {
			if (!this.hasData) {
				return {
					categories: [],
					series: []
				};
			}
			
			const { xdata, series } = this.data;
			
			// 转换为 qiun-data-charts 需要的格式
			const processedSeries = [];
			
			if (series && series.length > 0) {
				series.forEach(item => {
					if (item.data && item.data.length > 0) {
						processedSeries.push({
							name: item.name || '数据',
							data: item.data
						});
					}
				});
			}
			
			return {
				categories: xdata,
				series: processedSeries
			};
		},
		
		chartOptions() {
			return {
				color: ["#2194FF", "#00ff88", "#ff6b35", "#ff4d4d"],
				padding: [15, 15, 0, 15],
				enableScroll: false,
				legend: {
					show: true,
					position: "bottom",
					float: "center",
					padding: 5,
					margin: 5,
					backgroundColor: "rgba(0,0,0,0)",
					borderColor: "rgba(0,0,0,0)",
					borderWidth: 0,
					fontSize: 13,
					fontColor: "#ffffff",
					lineHeight: 11,
					hiddenColor: "#cccccc",
					itemGap: 10
				},
				xAxis: {
					disableGrid: false,
					gridType: "dash",
					gridColor: "rgba(33, 148, 255, 0.3)",
					fontColor: "#ffffff",
					fontSize: 11,
					rotateLabel: false,
					itemCount: 6,
					boundaryGap: "center"
				},
				yAxis: {
					disableGrid: false,
					gridType: "dash", 
					gridColor: "rgba(33, 148, 255, 0.3)",
					fontColor: "#ffffff",
					fontSize: 11,
					showTitle: false
				},
				extra: {
					line: {
						type: "curve",
						width: 2,
						activeType: "hollow",
						linearType: "none",
						onShadow: false,
						dataLabel: false,
						dataPointShape: "circle",
						dataPointShapeType: "hollow"
					},
					tooltip: {
						showBox: true,
						showArrow: true,
						showCategory: true,
						borderRadius: 4,
						borderWidth: 1,
						borderColor: "#2194FF",
						backgroundColor: "rgba(0, 0, 0, 0.8)",
						fontColor: "#ffffff",
						fontSize: 12
					}
				}
			};
		}
	},
	methods: {
		// 图表渲染完成回调
		onChartReady(event) {
			console.log('图表渲染完成:', event);
		},
		
		// 图表点击事件
		onChartClick(event) {
			console.log('图表点击:', event);
		}
	}
};
</script>

<style scoped>
.chart-container {
	width: 100%;
	height: 100%;
	position: relative;
}

.chart-wrapper {
	width: 100%;
	height: 100%;
}

.no-data-wrapper {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	height: 100%;
	min-height: 200rpx;
}

.no-data-text {
	color: rgba(255, 255, 255, 0.6);
	font-size: 28rpx;
}
</style> 