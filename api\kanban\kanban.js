import request from '@/utils/request'

// 查询看板标签
export function powerPointConfiglable(data) {
  return request({
    url: '/project/powerPointConfig/all',
    method: 'Post',
	data: data
  })
}

// 获取数据看板接口
export function powerPointConfigData(data) {
  return request({
    url: '/project/powerPointConfig/point/data',
    method: 'Post',
	data: data
  })
}

// 点击点位获取图表数据

export function powerPointConfigEcharts(data) {
  return request({
    url: '/device/deviceProperty/downSampling/list/wechat',
    method: 'Post',
	data: data
  })
}