<template>
	<view class="arr industrial-container">
		<AppHeader flex title="[CIMS]告警中心"  />
		<!-- 筛选器容器 -->
		<view class="selectors tech-panel">
			<view class="select-item">
				<view class="select-decorator"></view>
				<uni-data-select v-model="dictTypeVal" :clear="false" :localdata="dictType" placeholder="报警类型"
					@change="changeDictType" class="custom-select"></uni-data-select>
			</view>
			<view class="select-item">
				<view class="select-decorator"></view>
				<uni-data-select v-model="dealVal" placeholder="处理状态" :clear="false" :localdata="dealList"
					@change="changeDeal" class="custom-select"></uni-data-select>
			</view>
		</view>

		<!-- 告警统计面板 -->
		<view class="alarm-stats tech-panel">
			<view class="stat-item">
				<view class="stat-value">{{totalCount}}</view>
				<view class="stat-label">总告警数</view>
				<view class="pulse-dot"></view>
			</view>
			<view class="stat-item">
				<view class="stat-value">{{getUnhandledCount}}</view>
				<view class="stat-label">未处理</view>
				<view class="pulse-dot warning"></view>
			</view>
			<view class="stat-item">
				<view class="stat-value">{{getHandledCount}}</view>
				<view class="stat-label">已处理</view>
				<view class="pulse-dot success"></view>
			</view>
		</view>

		<scroll-view class="scroll-content" scroll-y="true" @scrolltolower="loadMore">
			<view v-for="(data, index) in alarmData" :key="index" class="content-data tech-card" :class="getStatusClass(data.level)" @click="openPopup(data)">
				<view class="tech-card-header">
					<view class="header-left">
						<view class="status-indicator" :class="getStatusClass(data.level)"></view>
						<view class="alarm-type">{{getTypeName(data.level)}}</view>
						</view>
					<view class="header-right" :class="data.dealFlag === 0 ? 'unhandled' : 'handled'">
						<view class="status-text">{{ getdealFlag(data.dealFlag) }}</view>
						<!-- <view class="corner-decorator"></view> -->
					</view>
				</view>

				<view class="tech-card-content">
					<view class="data-row">
						<view class="property-name">{{ data.propertyName }}</view>
						<view class="property-value">
							<text>{{ data.value }}</text>
							<text class="unit">{{data.unit}}</text>
							<view class="trend-indicator" :class="[getTrendClass(data.level), getStatusClass(data.level)]">
							<img v-if="data.level === 4 || data.level === 5 || data.level === 6" class="icon" src="static/images/tabs/arrow.svg" alt="icon" />
							<img v-else-if="data.level === 1 || data.level === 2 || data.level === 3" class="icon" src="static/images/tabs/arrowbottom.svg" alt="icon" />
							</view>
						</view>
					</view>

					<view class="time-info">
						<view class="time-row">
							<text class="time-label">首次报警</text>
							<text class="time-value">{{ data.createTime }}</text>
						</view>
						<view class="time-row">
							<text class="time-label">最近报警</text>
							<text class="time-value">{{ data.lastOccurTime }}</text>
						</view>
					</view>
				</view>
		</view>

		<!-- 没有更多数据提示 - 移到scroll-view内部 -->
		<view v-if="noMoreData" class="no-more-data"></view>
		</scroll-view>

		<view v-if="loading" class="loading tech-loading">
			<view class="loading-circle"></view>
			<text>加载中...</text>
		</view>
		<EmptyData
			:show="showEmptyData"
			title="暂无告警数据"
			description="当前筛选条件下没有找到相关告警信息"
		/>
	</view>
</template>

<script>
	import {
		alarmDict,
		alarmList
	} from "@/api/alarm/alarm.js";
	import {
		getProject
	} from "@/utils/auth.js";
	import EmptyData from "@/components/EmptyData/EmptyData.vue";

	export default {
		components: {
			EmptyData
		},
		data() {
			return {
				iconColor: "#f5f5f5",
				iconShow: true,
				selectedAlarm: {},
				alarmData: [],
				totalCount: 0, // 添加总数字段
				type: [],
				dictType: [],
				project: {},
				dealList: [
					{
						value: "",
						text: "全部",
					},
					{
						value: 1,
						text: "已处理",
					},
					{
						value: 0,
						text: "未处理",
					},
				],
				dictTypeVal: "",
				readVal: 0,
				dealVal: "",
				pageNum: 1,
				pageSize: 10,
				hasMoreData: true,
				loading: false,
				noMoreData: false,
				showEmptyData: false,
			};
		},
		computed: {
			// 获取未处理告警数量（基于当前页面显示的数据）
			getUnhandledCount() {
				return this.alarmData.filter(item => item.dealFlag === 0).length;
			},
			// 获取已处理告警数量（基于当前页面显示的数据）
			getHandledCount() {
				return this.alarmData.filter(item => item.dealFlag === 1).length;
			}
		},
		mounted() {
			this.getAlarmDict();
		},
		onShow() {
			this.getAlarmList();
		},
		methods: {
			// 获取告警状态样式类
			getStatusClass(level) {
				const levelNum = parseInt(level);
				switch(levelNum) {
					case 1: // 低限预警
					case 4: // 高限预警
						return 'warning-low'; // 预警级别 - 蓝色
					case 2: // 低限报警
					case 5: // 高限报警
						return 'warning-medium'; // 报警级别 - 橙色
					case 3: // 低限严重警告
					case 6: // 高限严重警告
						return 'warning-high'; // 严重警告级别 - 红色
					default:
						return 'normal'; // 正常/告警解除 - 绿色
				}
			},
			// 获取趋势指示器样式类
			getTrendClass(level) {
				const levelNum = parseInt(level);
				// 高限告警显示向上箭头（红色）
				if (levelNum === 4 || levelNum === 5 || levelNum === 6) {
					return 'up';
				}
				// 低限告警显示向下箭头（蓝色）
				else if (levelNum === 1 || levelNum === 2 || levelNum === 3) {
					return 'down';
				}
				return '';
			},
			getAlarmList() {
				if (this.loading) return;
				this.loading = true;
				this.project = getProject();
				
				const requestData = {
					projectId: this.project.id,
					readFlag: this.readVal,
				};
				
				// 只有当选择了具体的报警类型时才添加level参数
				if (this.dictTypeVal !== "") {
					requestData.level = this.dictTypeVal;
				}
				
				// 只有当选择了具体的处理状态时才添加dealFlag参数
				if (this.dealVal !== "") {
					requestData.dealFlag = this.dealVal;
				}
				
				const params = {
					pageNum: this.pageNum,
					pageSize: this.pageSize,
					data: requestData,
				};
				
				alarmList(params)
					.then((res) => {
						// 更新总数
						this.totalCount = res.data.total || 0;

						if (res.data.rows.length > 0) {
							// 添加动画延迟
							const newData = res.data.rows.map((item, index) => ({
								...item,
								animationDelay: `${index * 0.1}s`
							}));

							this.alarmData =
								this.pageNum === 1 ?
								newData :
								this.alarmData.concat(newData);
							this.hasMoreData = res.data.rows.length >= this.pageSize;
							// 只有当没有更多数据且已有数据时才显示"没有更多数据"
							this.noMoreData = !this.hasMoreData && this.alarmData.length > 0;
							this.showEmptyData = false;
						} else {
							this.alarmData = [];
							this.hasMoreData = false;
							this.noMoreData = false;
							// 第一页且没有数据时显示空数据状态
							this.showEmptyData = this.pageNum === 1;
						}
						this.loading = false;
					})
					.catch((err) => {
						this.loading = false;
						this.totalCount = 0; // 错误时重置总数
						console.error(err);
					});
			},
			// 获取告警字典
			getAlarmDict() {
				const params = {
					data: "alert_level",
					pageNum: 1,
					pageSize: 30,
				};
				alarmDict(params).then((res) => {
					if (res.data.length > 0) {
						const filteredData = res.data.filter(
							(item) => item.dictValue !== "-1"
						);
						this.dictType = [
							{
								text: "全部",
								value: "",
							},
							...filteredData.map((item) => {
								return {
									text: item.dictLabel,
									value: item.dictValue,
								};
							})
						];

						this.type = filteredData.map((item) => {
							return {
								id: item.dictValue,
								name: item.dictLabel,
							};
						});
					}
				});
			},
			getTypeName(type) {
				const typeStr = type.toString();
				const typeObj = this.type.find((item) => item.id.toString() === typeStr);
				return typeObj ? typeObj.name : "";
			},
			getdealFlag(type) {
				const typeStr = type.toString();
				const typeObj = this.dealList.find(
					(item) => item.value.toString() === typeStr
				);
				return typeObj ? typeObj.text : "";
			},
			getDealStyle(dealFlag) {
				if (dealFlag === 0) { // 未处理
					return {
						border: '1px solid rgba(33, 148, 255, 1)',
						backgroundColor: 'rgba(4, 47, 80, 1)',
						color: 'rgba(255, 255, 255, 1)'
					};
				} else if (dealFlag === 1) { // 已处理
					return {
						border: '1px solid rgba(54, 65, 77, 1)',
						backgroundColor: 'transparent',
						color: 'rgba(153, 153, 153, 1)'
					};
				}
				return {}; // 默认样式
			},
			openPopup(/* data */) {
				// if(data.readFlag===0){
				// 	const id=data.id.toString()
				// 	const params={
				// 		data:[
				// 			id
				// 		]
				// 	}
				// 	alarmRead(params)
				// 	.then(res=>{
				// 	})
				// }
				// const alarmType = this.type
				// const payload = {
				// 	data: data,
				// 	alarmType: alarmType
				// };
				// uni.navigateTo({
				// 	url: '/alarm/alarmDetails/index?data=' + JSON.stringify(payload)
				// });
			},
			changeDictType(value) {
				this.dictTypeVal = value;
				this.resetList();
			},
			changeRead(value) {
				this.readVal = value;
				this.resetList();
			},
			changeDeal(value) {
				this.dealVal = value;
				this.resetList();
			},
			resetList() {
				this.pageNum = 1;
				this.hasMoreData = true;
				this.noMoreData = false;
				this.showEmptyData = false;
				this.alarmData = [];
				this.totalCount = 0; // 重置总数
				this.getAlarmList();
			},
			loadMore() {
				if (this.hasMoreData) {
					this.pageNum++;
					this.getAlarmList();
				}
			},
		},
	};
</script>

<style scoped>
.industrial-container {
	background: linear-gradient(180deg, rgba(16, 24, 36, 0.95) 0%, rgba(12, 20, 28, 0.98) 100%);
	/* min-height: 100vh; */
	position: relative;
	display: flex;
	flex-direction: column;
	height: 100vh;
}

.industrial-container::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: 
		repeating-linear-gradient(0deg, transparent 0px, rgba(33, 148, 255, 0.03) 1px, transparent 2px),
		repeating-linear-gradient(90deg, transparent 0px, rgba(33, 148, 255, 0.03) 1px, transparent 2px);
	background-size: 20px 20px;
	pointer-events: none;
}

.tech-panel {
	background: rgba(18, 34, 46, 0.7);
	border: 1px solid rgba(33, 148, 255, 0.3);
	border-radius: 8px;
	padding: 15rpx;
	position: relative;
	margin: 20rpx;
}

.tech-panel::before {
	content: '';
	position: absolute;
	top: -2px;
	left: -2px;
	right: -2px;
	bottom: -2px;
	background: linear-gradient(90deg, rgba(33, 148, 255, 0.3), transparent);
	border-radius: 10px;
	z-index: -1;
	}
	
	.selectors {
		display: flex;
		justify-content: space-between;
		align-items: center;
		gap: 20rpx;
		flex-shrink: 0;
	}

	.select-item {
	flex: 1;
	position: relative;
}

.select-decorator {
	position: absolute;
	width: 6px;
	height: 6px;
	border: 1px solid rgba(33, 148, 255, 0.6);
	border-radius: 1px;
	}

.select-decorator:nth-child(1) {
	top: -2px;
	left: -2px;
}

.select-decorator:nth-child(2) {
	top: -2px;
	right: -2px;
}

.alarm-stats {
	display: flex;
	justify-content: space-around;
	padding: 20rpx;
	flex-shrink: 0;
}

.stat-item {
	text-align: center;
	position: relative;
}

.stat-value {
	font-size: 36rpx;
	color: #2194ff;
	font-weight: bold;
	text-shadow: 0 0 10px rgba(33, 148, 255, 0.3);
	}

.stat-label {
	font-size: 24rpx;
	color: #b9c8dc;
	margin-top: 5rpx;
}

.pulse-dot {
	width: 8rpx;
	height: 8rpx;
	border-radius: 50%;
	background: #2194ff;
	position: absolute;
	right: -15rpx;
	top: 50%;
	animation: pulse 2s infinite;
}

.pulse-dot.warning {
	background: #ff6b35;
}

.pulse-dot.success {
	background: #00ff88;
}

@keyframes pulse {
	0% {
		transform: scale(1);
		opacity: 1;
	}
	50% {
		transform: scale(1.5);
		opacity: 0.5;
	}
	100% {
		transform: scale(1);
		opacity: 1;
	}
}

.tech-card {
	background: rgba(18, 34, 46, 0.7);
	border: 1px solid rgba(33, 148, 255, 0.2);
	border-radius: 8px;
	margin: 20rpx;
	padding: 20rpx;
	position: relative;
	transition: all 0.3s ease;
	backdrop-filter: blur(10px);
}

/* 为报警和严重警告级别添加额外的视觉层次 */
.tech-card.warning-medium,
.tech-card.warning-high {
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
	backdrop-filter: blur(15px);
}

.tech-card.warning-medium {
	border: 1px solid rgba(255, 149, 0, 0.4);
}

.tech-card.warning-high {
	border: 1px solid rgba(255, 59, 48, 0.4);
}

/* 报警级别卡片呼吸效果 */
.tech-card.warning-medium {
	position: relative;
	overflow: hidden;
}

.tech-card.warning-medium::before {
	content: '';
	position: absolute;
	top: -3px;
	left: -3px;
	right: -3px;
	bottom: -3px;
	background: linear-gradient(45deg,
		rgba(255, 149, 0, 0.4),
		rgba(255, 149, 0, 0.1),
		rgba(255, 149, 0, 0.4),
		rgba(255, 149, 0, 0.1)
	);
	background-size: 400% 400%;
	border-radius: 12px;
	z-index: -1;
	animation: warningBreathing 3s ease-in-out infinite;
	filter: blur(1px);
}

.tech-card.warning-medium::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 149, 0, 0.05);
	border-radius: 8px;
	z-index: -1;
	animation: warningInnerGlow 3s ease-in-out infinite;
}

/* 严重警告级别卡片呼吸效果 */
.tech-card.warning-high {
	position: relative;
	overflow: hidden;
}

.tech-card.warning-high::before {
	content: '';
	position: absolute;
	top: -4px;
	left: -4px;
	right: -4px;
	bottom: -4px;
	background: linear-gradient(45deg,
		rgba(255, 59, 48, 0.5),
		rgba(255, 59, 48, 0.1),
		rgba(255, 59, 48, 0.5),
		rgba(255, 59, 48, 0.1)
	);
	background-size: 400% 400%;
	border-radius: 14px;
	z-index: -1;
	animation: criticalBreathing 2.5s ease-in-out infinite;
	filter: blur(1.5px);
}

.tech-card.warning-high::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 59, 48, 0.08);
	border-radius: 8px;
	z-index: -1;
	animation: criticalInnerGlow 2.5s ease-in-out infinite;
}

.tech-card:hover {
	transform: translateY(-2px);
	box-shadow: 0 4px 12px rgba(33, 148, 255, 0.1);
}

.tech-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 2px;
	background: linear-gradient(90deg, transparent, rgba(33, 148, 255, 0.5), transparent);
}

.tech-card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}

.header-left {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.status-indicator {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
	position: relative;
}

.status-indicator::after {
	content: '';
	position: absolute;
	top: -2px;
	left: -2px;
	right: -2px;
	bottom: -2px;
	border-radius: 50%;
	animation: statusPulse 2s infinite;
}

.status-indicator.warning-high {
	background: #ff3b30; /* 红色 - 严重警告级别 (低限严重警告 + 高限严重警告) */
	box-shadow: 0 0 8px rgba(255, 59, 48, 0.6), 0 0 16px rgba(255, 59, 48, 0.3);
	border: 1px solid rgba(255, 59, 48, 0.8);
}

.status-indicator.warning-medium {
	background: #ff9500; /* 橙色 - 报警级别 (低限报警 + 高限报警) */
	box-shadow: 0 0 6px rgba(255, 149, 0, 0.5), 0 0 12px rgba(255, 149, 0, 0.2);
	border: 1px solid rgba(255, 149, 0, 0.7);
}

.status-indicator.warning-low {
	background: #007aff; /* 蓝色 - 预警级别 (低限预警 + 高限预警) */
	box-shadow: 0 0 4px rgba(0, 122, 255, 0.4);
}

.status-indicator.normal {
	background: #34c759; /* 绿色 - 正常/告警解除 */
	box-shadow: 0 0 4px rgba(52, 199, 89, 0.4);
}

.status-indicator.warning-high::after {
	animation: warningHighPulse 1.2s infinite;
}

.status-indicator.warning-medium::after {
	animation: warningMediumPulse 1.8s infinite;
}

.status-indicator.warning-low::after {
	animation: warningLowPulse 2.5s infinite;
}

.status-indicator.normal::after {
	animation: normalPulse 3s infinite;
}

@keyframes warningHighPulse {
	0% {
		box-shadow: 0 0 8px rgba(255, 59, 48, 0.6), 0 0 16px rgba(255, 59, 48, 0.3), 0 0 0 0 rgba(255, 59, 48, 0.8);
	}
	50% {
		box-shadow: 0 0 12px rgba(255, 59, 48, 0.8), 0 0 24px rgba(255, 59, 48, 0.4), 0 0 0 10px rgba(255, 59, 48, 0);
	}
	100% {
		box-shadow: 0 0 8px rgba(255, 59, 48, 0.6), 0 0 16px rgba(255, 59, 48, 0.3), 0 0 0 0 rgba(255, 59, 48, 0.8);
	}
}

@keyframes warningMediumPulse {
	0% {
		box-shadow: 0 0 6px rgba(255, 149, 0, 0.5), 0 0 12px rgba(255, 149, 0, 0.2), 0 0 0 0 rgba(255, 149, 0, 0.7);
	}
	50% {
		box-shadow: 0 0 10px rgba(255, 149, 0, 0.7), 0 0 20px rgba(255, 149, 0, 0.3), 0 0 0 8px rgba(255, 149, 0, 0);
	}
	100% {
		box-shadow: 0 0 6px rgba(255, 149, 0, 0.5), 0 0 12px rgba(255, 149, 0, 0.2), 0 0 0 0 rgba(255, 149, 0, 0.7);
	}
}

@keyframes warningLowPulse {
	0% {
		box-shadow: 0 0 0 0 rgba(0, 122, 255, 0.5);
	}
	70% {
		box-shadow: 0 0 0 4px rgba(0, 122, 255, 0);
	}
	100% {
		box-shadow: 0 0 0 0 rgba(0, 122, 255, 0);
	}
}

@keyframes normalPulse {
	0% {
		box-shadow: 0 0 0 0 rgba(52, 199, 89, 0.3);
	}
	70% {
		box-shadow: 0 0 0 3px rgba(52, 199, 89, 0);
	}
	100% {
		box-shadow: 0 0 0 0 rgba(52, 199, 89, 0);
	}
}

/* 报警级别呼吸动画 */
@keyframes warningBreathing {
	0% {
		background-position: 0% 50%;
		opacity: 0.6;
		transform: scale(1);
	}
	25% {
		background-position: 100% 50%;
		opacity: 0.8;
		transform: scale(1.01);
	}
	50% {
		background-position: 100% 100%;
		opacity: 1;
		transform: scale(1.02);
	}
	75% {
		background-position: 0% 100%;
		opacity: 0.8;
		transform: scale(1.01);
	}
	100% {
		background-position: 0% 50%;
		opacity: 0.6;
		transform: scale(1);
	}
}

/* 严重警告级别呼吸动画 */
@keyframes criticalBreathing {
	0% {
		background-position: 0% 50%;
		opacity: 0.7;
		transform: scale(1);
	}
	20% {
		background-position: 50% 0%;
		opacity: 0.9;
		transform: scale(1.015);
	}
	40% {
		background-position: 100% 50%;
		opacity: 1;
		transform: scale(1.03);
	}
	60% {
		background-position: 50% 100%;
		opacity: 0.9;
		transform: scale(1.015);
	}
	80% {
		background-position: 0% 50%;
		opacity: 0.8;
		transform: scale(1.01);
	}
	100% {
		background-position: 0% 50%;
		opacity: 0.7;
		transform: scale(1);
	}
}

/* 报警级别内发光动画 */
@keyframes warningInnerGlow {
	0% {
		opacity: 0.3;
	}
	50% {
		opacity: 0.6;
	}
	100% {
		opacity: 0.3;
	}
}

/* 严重警告级别内发光动画 */
@keyframes criticalInnerGlow {
	0% {
		opacity: 0.4;
	}
	50% {
		opacity: 0.8;
	}
	100% {
		opacity: 0.4;
	}
}

.alarm-type {
	font-size: 28rpx;
	color: #f5f5f5;
	font-weight: 500;
}

.header-right {
	display: flex;
	align-items: center;
	padding: 4rpx 12rpx;
	border-radius: 4px;
	position: relative;
}

.header-right.unhandled {
	background: rgba(255, 59, 48, 0.1);
	border: 1px solid rgba(255, 59, 48, 0.3);
}

.header-right.handled {
	background: rgba(52, 199, 89, 0.1);
	border: 1px solid rgba(52, 199, 89, 0.3);
}

.status-text {
	font-size: 24rpx;
	color: #f5f5f5;
}

.corner-decorator {
	position: absolute;
	width: 6px;
	height: 6px;
	border: 1px solid currentColor;
	opacity: 0.6;
}

.tech-card-content {
	padding: 10rpx 0;
	}

	.data-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
	margin-bottom: 15rpx;
}

.property-name {
		font-size: 28rpx;
		color: #b9c8dc;
}

.property-value {
	display: flex;
	align-items: center;
	gap: 8rpx;
	font-size: 32rpx;
	color: #f5f5f5;
	font-weight: 500;
}

.unit {
	font-size: 24rpx;
	color: #b9c8dc;
	margin-left: 4rpx;
}

.trend-indicator {
	display: flex;
	align-items: center;
	margin-left: 8rpx;
}

.trend-indicator .icon {
	width: 24rpx;
	height: 24rpx;
	transition: all 0.3s ease;
}

/* 趋势指示器颜色与告警级别保持一致 */
.trend-indicator.up.warning-low .icon,
.trend-indicator.down.warning-low .icon {
	/* 蓝色 #007aff */
	filter: brightness(0) saturate(100%) invert(40%) sepia(100%) saturate(2000%) hue-rotate(200deg) brightness(110%) contrast(110%);
}

.trend-indicator.up.warning-medium .icon,
.trend-indicator.down.warning-medium .icon {
	/* 橙色 #ff9500 */
	filter: brightness(0) saturate(100%) invert(60%) sepia(100%) saturate(2000%) hue-rotate(15deg) brightness(110%) contrast(110%);
}

.trend-indicator.up.warning-high .icon,
.trend-indicator.down.warning-high .icon {
	/* 红色 #ff3b30 */
	filter: brightness(0) saturate(100%) invert(25%) sepia(100%) saturate(2000%) hue-rotate(350deg) brightness(110%) contrast(110%);
}

/* 为趋势指示器添加发光效果，与状态指示器保持一致 */
.trend-indicator.up.warning-medium .icon,
.trend-indicator.down.warning-medium .icon {
	filter: brightness(0) saturate(100%) invert(60%) sepia(100%) saturate(2000%) hue-rotate(15deg) brightness(110%) contrast(110%) drop-shadow(0 0 4px rgba(255, 149, 0, 0.6));
}

.trend-indicator.up.warning-high .icon,
.trend-indicator.down.warning-high .icon {
	filter: brightness(0) saturate(100%) invert(25%) sepia(100%) saturate(2000%) hue-rotate(350deg) brightness(110%) contrast(110%) drop-shadow(0 0 6px rgba(255, 59, 48, 0.6));
}

.time-info {
	border-top: 1px solid rgba(185, 200, 220, 0.1);
	padding-top: 15rpx;
	margin-top: 15rpx;
	}

.time-row {
	display: flex;
	justify-content: space-between;
	margin-bottom: 8rpx;
	font-size: 24rpx;
}

.time-label {
	color: #b9c8dc;
}

.time-value {
		color: #f5f5f5;
}

.tech-loading {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 30rpx;
	}

.loading-circle {
	width: 40rpx;
	height: 40rpx;
	border: 2px solid rgba(33, 148, 255, 0.1);
	border-top-color: #2194ff;
	border-radius: 50%;
	animation: spin 1s linear infinite;
		margin-bottom: 10rpx;
}

@keyframes spin {
	to {
		transform: rotate(360deg);
	}
	}

.scroll-content {
	flex: 1;
	overflow-y: auto;
}

.no-more-data {
	text-align: center;
	color: #b9c8dc;
	font-size: 28rpx;
	padding: 40rpx 20rpx;
	background: rgba(18, 34, 46, 0.3);
	margin: 20rpx;
	border-radius: 8rpx;
	border: 1px solid rgba(185, 200, 220, 0.1);
	position: relative;
}

.no-more-data::before {
	content: '';
	position: absolute;
	top: 50%;
	left: 20rpx;
	right: 20rpx;
	height: 1px;
	background: linear-gradient(90deg, transparent, rgba(185, 200, 220, 0.3), transparent);
	transform: translateY(-50%);
	z-index: 1;
}

.no-more-data::after {
	content: '没有更多数据';
	position: relative;
	background: rgba(18, 34, 46, 0.8);
	padding: 0 20rpx;
	z-index: 2;
}



::v-deep .uni-select__input-text {
		color: #f5f5f5;
}

::v-deep .uni-select__selector {
	background: rgba(27, 34, 51, 1);
	color: rgba(255, 255, 255, 1);
	border: none;
}

::v-deep .uni-select {
	border: none;
	}
</style>