<template>
  <view class="mine-page">
    <!-- 使用AppHeader组件 -->
    <AppHeader flex title="[CIMS] 个人中心"  :bg="bg"/>

    <view class="mine-content">
      <!-- 用户信息卡片 -->
      <view class="user-card">
        <view class="card-frame">
          <view class="corner-card tl"></view>
          <view class="corner-card tr"></view>
          <view class="corner-card bl"></view>
          <view class="corner-card br"></view>
        </view>

        <view class="user-info-section">
          <!-- 头像区域 -->
          <view class="avatar-section" @click="handleToAvatar">
            <view class="avatar-frame">
              <view v-if="!avatar" class="default-avatar">
                <text class="avatar-icon">👤</text>
              </view>
              <image v-if="avatar" :src="avatar" class="user-avatar" mode="aspectFill"></image>
              <view class="avatar-border"></view>
            </view>
            <view class="avatar-status" :class="{ 'online': name }"></view>
          </view>

          <!-- 用户信息 -->
          <view class="user-details">
            <view v-if="!name" @click="handleToLogin" class="login-prompt">
              <text class="login-text">点击登录</text>
              <text class="login-arrow">→</text>
            </view>
            <view v-if="name" class="user-profile">
              <view class="user-name">{{ user.nickName || '未设置昵称' }}</view>
              <view class="user-phone">{{ user.phonenumber || '未绑定手机' }}</view>
              <view class="user-status">
                <text class="status-dot"></text>
                <text class="status-text">在线</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 项目信息卡片 -->
      <view class="project-card">
        <view class="card-frame">
          <view class="corner-card tl"></view>
          <view class="corner-card tr"></view>
          <view class="corner-card bl"></view>
          <view class="corner-card br"></view>
        </view>

        <view class="project-header">
          <view class="project-icon">🏢</view>
          <view class="project-title">当前项目</view>
        </view>

        <view class="project-info">
          <view class="project-name">{{ project.name || "环际低碳" }}</view>
          <view class="project-switch" @click="handleBuilding('project')">
            <text class="switch-text">切换项目</text>
            <text class="switch-icon">⚙️</text>
          </view>
        </view>
      </view>
      

      <!-- 详细信息卡片 -->
      <view class="details-card" v-if="name">
        <view class="card-frame">
          <view class="corner-card tl"></view>
          <view class="corner-card tr"></view>
          <view class="corner-card bl"></view>
          <view class="corner-card br"></view>
        </view>

        <view class="details-header">
          <view class="details-icon">📋</view>
          <view class="details-title">详细信息</view>
        </view>

        <view class="details-list">
          <view class="detail-item" @click="handleToInfo">
            <view class="detail-label">
              <text class="label-icon">👤</text>
              <text class="label-text">昵称</text>
            </view>
            <view class="detail-value">
              <text class="value-text">{{ user.nickName || '未设置' }}</text>
              <text class="detail-arrow">›</text>
            </view>
          </view>

          <view class="detail-item">
            <view class="detail-label">
              <text class="label-icon">📱</text>
              <text class="label-text">手机号码</text>
            </view>
            <view class="detail-value">
              <text class="value-text">{{ user.phonenumber || '未绑定' }}</text>
            </view>
          </view>

          <view class="detail-item">
            <view class="detail-label">
              <text class="label-icon">📧</text>
              <text class="label-text">邮箱</text>
            </view>
            <view class="detail-value">
              <text class="value-text">{{ user.email || '未设置' }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 操作按钮区域 -->
      <view class="action-section">
        <view class="action-card" v-if="rolestate" @click="handleShare">
          <view class="action-icon">📤</view>
          <view class="action-text">分享小程序</view>
          <view class="action-arrow">›</view>
        </view>

        <view class="action-card logout" v-if="name" @click="handleLogout">
          <view class="action-icon">🚪</view>
          <view class="action-text">安全退出</view>
          <view class="action-arrow">›</view>
        </view>

        <view class="action-card login" v-if="!name" @click="handleToLogin">
          <view class="action-icon">🔑</view>
          <view class="action-text">立即登录</view>
          <view class="action-arrow">›</view>
        </view>
      </view>
      
    </view>

    <u-popup
      :show="show"
      :round="20"
      @close="close"
      bgColor="transparent"
      @open="open"
      :overlay="true"
      :overlayOpacity="0.7"
      mode="center"
    >
      <view class="share-popup">
        <!-- 弹窗头部 -->
        <view class="popup-header">
          <view class="header-decoration">
            <view class="decoration-line left"></view>
            <view class="popup-icon">👥</view>
            <view class="decoration-line right"></view>
          </view>
          <view class="popup-title">邀请好友加入</view>
          <view class="popup-subtitle">填写好友信息，邀请使用CIMS系统</view>
          <view class="close-btn" @click="close">✕</view>
        </view>

        <!-- 表单内容 -->
        <view class="popup-content">
          <view class="form-section">
            <view class="input-group">
              <view class="input-label">
                <view class="label-icon">👤</view>
                <text class="label-text">好友姓名</text>
              </view>
              <view class="input-wrapper">
                <input
                  class="form-input"
                  v-model="friendName"
                  placeholder="请输入好友姓名"
                  placeholder-class="input-placeholder"
                  maxlength="20"
                />
                <view class="input-border"></view>
              </view>
            </view>

            <view class="input-group">
              <view class="input-label">
                <view class="label-icon">📱</view>
                <text class="label-text">手机号码</text>
              </view>
              <view class="input-wrapper">
                <input
                  class="form-input"
                  v-model="friendPhone"
                  placeholder="请输入11位手机号码"
                  placeholder-class="input-placeholder"
                  type="number"
                  maxlength="11"
                />
                <view class="input-border"></view>
              </view>
            </view>
          </view>

          <!-- 提示信息 -->
          <view class="tips-section">
            <view class="tips-icon">💡</view>
            <view class="tips-content">
              <text class="tips-title">温馨提示</text>
              <text class="tips-text">初始密码为：123456</text>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="action-buttons">
            <button class="cancel-btn" @click="close">
              <text class="btn-text">取消</text>
            </button>
            <button class="submit-btn" @click="submitFriendInfo" :disabled="!canSubmit">
              <view class="btn-content">
                <text class="btn-icon">📤</text>
                <text class="btn-text">发送邀请</text>
              </view>
              <view class="btn-glow"></view>
            </button>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { getUserProfile } from "@/api/system/user";
import { changeTimesStamp } from "@/utils/util";
import { getProject, getRole } from "@/utils/auth";
import { addUser } from "@/api/mine/api.js";

export default {
  data() {
    return {
      show: false,
      project: {},
      name: this.$store.state.user.name,
      user: {},
      roleGroup: "",
	  role:[],
      postGroup: "",
      friendName: "",
      friendPhone: "",
	  rolestate:false,
	  bg: '#131b2e',
    };
  },
  onLoad() {
    this.getUser();
  },
  onShow() {
    this.project = getProject();
	this.role=getRole()
	if (this.role.length > 0) {
	    // 检查是否存在至少一个角色的 roleKey 不为 'common'
	    this.rolestate = this.role.some(item => item.roleKey !== 'common');
	  } else {
	    // 如果没有角色，则默认不显示 handleShare 按钮
	    this.rolestate = false;
	  }
  },
  computed: {
    avatar() {
      return this.$store.state.user.avatar;
    },
    windowHeight() {
      return uni.getSystemInfoSync().windowHeight - 50;
    },
    canSubmit() {
      return this.friendName.trim() && this.friendPhone.trim() && this.friendPhone.length === 11;
    },
  },
  methods: {
	   openOfficialAccount() {
	        // 确保在微信小程序环境下调用
			wx.openOfficialAccountProfile({
			  // 替换为您的公众号原始ID
			  officialAccount: 'gh_bf77eaba55fa',
			  success(res) {
			    console.log('成功打开公众号主页', res);
			  },
			  fail(err) {
			    console.error('打开公众号主页失败', err);
			  }
			});
	        // if (uni.getSystemInfoSync().platform === 'wechat') {
	        //   wx.openOfficialAccountProfile({
	        //     // 替换为您的公众号原始ID
	        //     officialAccount: 'gh_bf77eaba55fa',
	        //     success(res) {
	        //       console.log('成功打开公众号主页', res);
	        //     },
	        //     fail(err) {
	        //       console.error('打开公众号主页失败', err);
	        //     }
	        //   });
	        // } else {
	        //   uni.showToast({
	        //     title: '仅支持在微信小程序中使用',
	        //     icon: 'none'
	        //   });
	        // }
	      },
    submitFriendInfo() {

      const param = {
        data: {
          userName: this.friendPhone,
          nickName: this.friendName,
          password: "123456",
          phonenumber: this.friendPhone,
          status: 0,
		  project: this.project.id,
        },
      };
      addUser(param)
        .then((res) => {
          if (res.code == 200) {
            uni.showToast({
              title: "提交成功",
              duration: 2000,
            });
            this.show = false;
            this.friendName = "";
            this.friendPhone = "";
          } else {
            uni.showToast({
              title: "提交成功",
              duration: 2000,
            });
            this.show = false;
            this.friendName = "";
            this.friendPhone = "";
          }
        })
        .catch(() => {
          uni.showToast({
            title: "提交失败",
            duration: 2000,
          });
          this.show = false;
          this.friendName = "";
          this.friendPhone = "";
        });
    },
    changeInput() {
      // 输入变化处理
    },
    open() {
    },
    close() {
      this.show = false;
      this.friendName = "";
      this.friendPhone = "";
    },
    // 分享
    handleShare() {
      this.show = true;
    },
	 clearSpecificStorage(keys) {
	  keys.forEach(key => uni.removeStorageSync(key));
	},
    handleLogout() {
      this.$modal.confirm("确定注销并退出系统吗？").then(() => {
        this.$store.dispatch("LogOut").then(() => {
          // clearStorage()
		  this.clearSpecificStorage(['App-Token', 'client_id','role','storage_data','projectList','project']);
          this.$tab.reLaunch("/pages/index");
        });
      });
    },
    getUser() {
      getUserProfile().then((res) => {
        this.user = res.data.user;
        this.user.createTime = changeTimesStamp(res.data.createTime);
        this.roleGroup = res.roleGroup;
        this.postGroup = res.postGroup;
      });
    },
    handleToInfo() {
      this.$tab.navigateTo("/mine/info/index");
    },
    handleToEditInfo() {
      this.$tab.navigateTo("/mine/info/edit");
    },
    handleToSetting() { 
      this.$tab.navigateTo("/mine/setting/index");
    },
    handleToShare() {},
    handleToLogin() {
      this.$tab.reLaunch("/pages/login");
    },
    handleToAvatar() {
      this.$tab.navigateTo("/pages/mine/avatar/index");
    },

    handleBuilding(pageName) {
      switch (pageName) {
        case "project":
          uni.navigateTo({
            url: "/projectManagement/index",
          });
          break;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
/* 主容器 */
.mine-page {
  height: 100vh; /* 使用固定高度而不是最小高度 */
  background: linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 50%, #0f1419 100%);
  position: relative;
  overflow: hidden; /* 防止主容器滚动 */
}


/* 主要内容区域 - 参考首页模式 */
.mine-content {
  background: transparent; /* 使用透明背景，让主容器的渐变背景显示 */
  height: calc(100vh - 120rpx); /* 使用固定高度，为AppHeader留出空间 */
  padding: 30rpx 20rpx;
  position: relative;
  overflow-y: auto; /* 内容超出时才滚动 */
}

/* 通用卡片样式 */
.user-card, .project-card, .details-card {
  position: relative;
  background: linear-gradient(135deg, #1a2332 0%, #0f1419 100%);
  border: 1rpx solid #2a3441;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  padding: 30rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
}

.card-frame {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  border-radius: 20rpx;
}

.corner-card {
  position: absolute;
  width: 15rpx;
  height: 15rpx;
  border: 1rpx solid rgba(33, 148, 255, 0.5);
}

.corner-card.tl {
  top: 8rpx;
  left: 8rpx;
  border-right: none;
  border-bottom: none;
}

.corner-card.tr {
  top: 8rpx;
  right: 8rpx;
  border-left: none;
  border-bottom: none;
}

.corner-card.bl {
  bottom: 8rpx;
  left: 8rpx;
  border-right: none;
  border-top: none;
}

.corner-card.br {
  bottom: 8rpx;
  right: 8rpx;
  border-left: none;
  border-top: none;
}

/* 用户信息卡片 */
.user-info-section {
  display: flex;
  align-items: center;
  gap: 30rpx;
}

.avatar-section {
  position: relative;
  cursor: pointer;
}

.avatar-frame {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(33, 148, 255, 0.2) 0%, rgba(0, 255, 136, 0.1) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.default-avatar {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
}

.avatar-icon {
  font-size: 60rpx;
  color: rgba(255, 255, 255, 0.6);
}

.user-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.avatar-border {
  position: absolute;
  top: -2rpx;
  left: -2rpx;
  right: -2rpx;
  bottom: -2rpx;
  border: 2rpx solid rgba(33, 148, 255, 0.4);
  border-radius: 50%;
  animation: avatarPulse 2s ease-in-out infinite;
}

.avatar-status {
  position: absolute;
  bottom: 5rpx;
  right: 5rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background: #ff4444;
  border: 2rpx solid #ffffff;
}

.avatar-status.online {
  background: #00ff88;
  box-shadow: 0 0 10rpx rgba(0, 255, 136, 0.5);
}
/* 用户详情 */
.user-details {
  flex: 1;
}

.login-prompt {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: linear-gradient(135deg, rgba(33, 148, 255, 0.1) 0%, rgba(0, 255, 136, 0.05) 100%);
  border: 1rpx solid rgba(33, 148, 255, 0.3);
  border-radius: 15rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.login-prompt:active {
  transform: scale(0.98);
  background: linear-gradient(135deg, rgba(33, 148, 255, 0.2) 0%, rgba(0, 255, 136, 0.1) 100%);
}

.login-text {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 500;
}

.login-arrow {
  font-size: 24rpx;
  color: #2194FF;
  font-weight: bold;
}

.user-profile {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.user-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 0 8rpx rgba(33, 148, 255, 0.3);
}

.user-phone {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

.user-status {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #00ff88;
  box-shadow: 0 0 8rpx rgba(0, 255, 136, 0.5);
  animation: statusPulse 2s ease-in-out infinite;
}

.status-text {
  font-size: 20rpx;
  color: #00ff88;
  font-weight: 500;
}

/* 项目信息卡片 */
.project-header, .details-header {
  display: flex;
  align-items: center;
  gap: 15rpx;
  margin-bottom: 25rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid rgba(33, 148, 255, 0.2);
}

.project-icon, .details-icon {
  font-size: 32rpx;
}

.project-title, .details-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 0 8rpx rgba(33, 148, 255, 0.3);
}

.project-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.project-name {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.project-switch {
  display: flex;
  align-items: center;
  gap: 10rpx;
  padding: 15rpx 25rpx;
  background: linear-gradient(135deg, rgba(33, 148, 255, 0.15) 0%, rgba(0, 255, 136, 0.1) 100%);
  border: 1rpx solid rgba(33, 148, 255, 0.4);
  border-radius: 25rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.project-switch:active {
  transform: scale(0.95);
  background: linear-gradient(135deg, rgba(33, 148, 255, 0.25) 0%, rgba(0, 255, 136, 0.15) 100%);
}

.switch-text {
  font-size: 22rpx;
  color: #2194FF;
  font-weight: 500;
}

.switch-icon {
  font-size: 20rpx;
}

/* 详细信息列表 */
.details-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 25rpx 20rpx;
  background: rgba(26, 35, 50, 0.5);
  border: 1rpx solid #2a3441;
  border-radius: 15rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.detail-item:active {
  transform: scale(0.98);
  background: linear-gradient(135deg, rgba(33, 148, 255, 0.1) 0%, rgba(0, 255, 136, 0.05) 100%);
}

.detail-label {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.label-icon {
  font-size: 24rpx;
}

.label-text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.detail-value {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.value-text {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 500;
}

.detail-arrow {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.5);
  font-weight: bold;
}

/* 操作按钮区域 */
.action-section {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-top: 20rpx;
  margin-bottom: 20rpx; /* 距离底部20rpx */
}

.action-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 25rpx;
  background: linear-gradient(135deg, #1a2332 0%, #0f1419 100%);
  border: 1rpx solid #2a3441;
  border-radius: 20rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.action-card:active::before {
  left: 100%;
}

.action-card:active {
  transform: scale(0.98);
  background: linear-gradient(135deg, rgba(33, 148, 255, 0.2) 0%, rgba(0, 255, 136, 0.1) 100%);
}

.action-card.logout {
  background: linear-gradient(135deg, rgba(255, 68, 68, 0.1) 0%, rgba(255, 149, 0, 0.05) 100%);
  border-color: rgba(255, 68, 68, 0.3);
}

.action-card.logout:active {
  background: linear-gradient(135deg, rgba(255, 68, 68, 0.2) 0%, rgba(255, 149, 0, 0.1) 100%);
}

.action-card.login {
  background: linear-gradient(135deg, rgba(0, 255, 136, 0.1) 0%, rgba(33, 148, 255, 0.05) 100%);
  border-color: rgba(0, 255, 136, 0.3);
}

.action-card.login:active {
  background: linear-gradient(135deg, rgba(0, 255, 136, 0.2) 0%, rgba(33, 148, 255, 0.1) 100%);
}

.action-icon {
  font-size: 32rpx;
}

.action-text {
  flex: 1;
  margin-left: 20rpx;
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 500;
}

.action-arrow {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  font-weight: bold;
}

/* 动画效果 */
@keyframes avatarPulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

@keyframes statusPulse {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes cardFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5rpx);
  }
}

/* 分享弹窗样式 */
.share-popup {
  width: 650rpx;
  max-width: 90vw;
  background: linear-gradient(135deg, rgba(10, 15, 28, 0.98) 0%, rgba(26, 31, 46, 0.95) 50%, rgba(15, 20, 25, 0.98) 100%);
  border-radius: 25rpx;
  border: 2rpx solid rgba(33, 148, 255, 0.4);
  backdrop-filter: blur(20rpx);
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.5), 0 0 40rpx rgba(33, 148, 255, 0.2);
  position: relative;
  overflow: hidden;
  margin: 0 auto;
}

.share-popup::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(90deg, transparent, rgba(33, 148, 255, 0.8), transparent);
}

/* 弹窗头部 */
.popup-header {
  position: relative;
  padding: 40rpx 30rpx 30rpx;
  text-align: center;
  border-bottom: 1rpx solid rgba(33, 148, 255, 0.2);
}

.header-decoration {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.decoration-line {
  height: 2rpx;
  width: 80rpx;
  background: linear-gradient(90deg, transparent, rgba(33, 148, 255, 0.6), transparent);
}

.decoration-line.left {
  background: linear-gradient(90deg, transparent, rgba(33, 148, 255, 0.6));
}

.decoration-line.right {
  background: linear-gradient(90deg, rgba(33, 148, 255, 0.6), transparent);
}

.popup-icon {
  font-size: 40rpx;
  margin: 0 20rpx;
  filter: drop-shadow(0 0 10rpx rgba(33, 148, 255, 0.5));
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 0 15rpx rgba(33, 148, 255, 0.5);
  margin-bottom: 10rpx;
}

.popup-subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
}

.close-btn {
  position: absolute;
  top: 20rpx;
  right: 25rpx;
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-btn:active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.9);
}

/* 弹窗内容 */
.popup-content {
  padding: 30rpx;
}

.form-section {
  margin-bottom: 30rpx;
}

.input-group {
  margin-bottom: 30rpx;
}

.input-label {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.label-icon {
  font-size: 24rpx;
  margin-right: 10rpx;
  filter: drop-shadow(0 0 5rpx rgba(33, 148, 255, 0.5));
}

.label-text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.input-wrapper {
  position: relative;
}

.form-input {
  width: 100%;
  height: 80rpx;
  background: rgba(33, 148, 255, 0.08);
  border: 1rpx solid rgba(33, 148, 255, 0.3);
  border-radius: 15rpx;
  padding: 0 25rpx;
  font-size: 26rpx;
  color: #ffffff;
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: rgba(33, 148, 255, 0.6);
  background: rgba(33, 148, 255, 0.12);
  box-shadow: 0 0 20rpx rgba(33, 148, 255, 0.2);
}

.input-placeholder {
  color: rgba(255, 255, 255, 0.4) !important;
  font-size: 24rpx !important;
}

.input-border {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 2rpx;
  width: 0;
  background: linear-gradient(90deg, #2194FF, #00ff88);
  transition: width 0.3s ease;
}

.form-input:focus + .input-border {
  width: 100%;
}

/* 提示信息 */
.tips-section {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  background: rgba(0, 255, 136, 0.05);
  border: 1rpx solid rgba(0, 255, 136, 0.2);
  border-radius: 15rpx;
  margin-bottom: 40rpx;
}

.tips-icon {
  font-size: 28rpx;
  margin-right: 15rpx;
  margin-top: 5rpx;
  filter: drop-shadow(0 0 8rpx rgba(0, 255, 136, 0.5));
}

.tips-content {
  flex: 1;
}

.tips-title {
  display: block;
  font-size: 24rpx;
  color: #00ff88;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.tips-text {
  display: block;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 20rpx;
}

.cancel-btn, .submit-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 15rpx;
  border: none;
  font-size: 26rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  color: rgba(255, 255, 255, 0.8);
}

.cancel-btn .btn-text {
  font-size: 26rpx;
  font-weight: 600;
}

.cancel-btn:active {
  background: rgba(255, 255, 255, 0.15);
  transform: scale(0.98);
}

.submit-btn {
  background: linear-gradient(135deg, #2194FF 0%, #00ff88 100%);
  color: #ffffff;
  box-shadow: 0 8rpx 25rpx rgba(33, 148, 255, 0.3);
  position: relative;
}

.submit-btn:disabled {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.4);
  box-shadow: none;
  cursor: not-allowed;
}

.submit-btn:not(:disabled):active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 15rpx rgba(33, 148, 255, 0.5);
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  position: relative;
  z-index: 2;
}

.btn-icon {
  font-size: 24rpx;
}

.btn-text {
  font-size: 26rpx;
  font-weight: 600;
}

.btn-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.submit-btn:not(:disabled):active .btn-glow {
  left: 100%;
}

/* 响应式设计 */
@media (max-width: 320px) {
  .header-title {
    font-size: 28rpx;
  }

  .user-name {
    font-size: 28rpx;
  }

  .project-name {
    font-size: 24rpx;
  }

  .share-popup {
    width: 95vw;
    margin: 0 auto;
  }

  .popup-title {
    font-size: 28rpx;
  }

  .popup-subtitle {
    font-size: 22rpx;
  }

  .action-buttons {
    flex-direction: column;
    gap: 15rpx;
  }

  .cancel-btn, .submit-btn {
    width: 100%;
  }
}

/* 全局样式覆盖 */
::v-deep .uni-input {
  color: #ffffff !important;
  background: transparent !important;
}

::v-deep .u-row {
  border-bottom: 1rpx solid rgba(33, 148, 255, 0.2);
}

::v-deep .u-row:last-child {
  border-bottom: none;
}
</style>
