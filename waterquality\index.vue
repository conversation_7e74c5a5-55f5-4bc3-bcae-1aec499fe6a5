<template>
	<view class="arr">
		<!-- 工业风格背景装饰 -->
		<view class="tech-lines">
			<view class="line line-1"></view>
			<view class="line line-2"></view>
			<view class="line line-3"></view>
		</view>
		<AppHeader flex title="[CIMS]水质报告" />
		<view v-if="this.tabList.length>0">
			<view class="content">
				<!-- 标题区域 -->
				<view class="section-header">
					<view class="title-box">
						<text class="title-text">水质数据监测</text>
						<view class="title-line"></view>
					</view>
					<view class="data-count">
						<text class="count-text">{{ contentData.length }}项数据</text>
					</view>
				</view>
				<view class="content-tab">
					<u-tabs :list="tabList" @click="clickTab" :inactiveStyle="inactiveStyle" :activeStyle="activeStyle"
						:bar-style="barStyle">
					</u-tabs>
				</view>
				<view class="content-data">
					<view v-for="(data, index) in contentData" 
						:key="index" 
						class="data-row"
						:style="{ 'animation-delay': index * 0.1 + 's' }"
						@click="waterEchar(data)"
					>
						<view class="data-card">
							<view class="card-corner top-left"></view>
							<view class="card-corner top-right"></view>
							<view class="card-corner bottom-left"></view>
							<view class="card-corner bottom-right"></view>
							<view class="data-text">{{ data.name }}</view>
							<view class="data-unit">
								<view class="data-unit-value">{{ data.value != null ? data.value : "" }}</view>
								<view class="custom-span">{{data.unit !=null?data.unit:""}}</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<u-loadmore :loadmoreText="loadmoreText" color="#f5f5f5" lineColor="#1CD29B" line />
			<view v-if="$auth.hasPermi('project:powerPointConfig:device:data:add')" @click="btnWriteWater" class="btn">
				<view class="btn-content">填写水质报告</view>
				<view class="btn-border"></view>
			</view>
		</view>
		<!-- 空数据组件 -->
		<EmptyData
			:show="tabList.length === 0"
			title="暂无水质数据"
			description="当前项目下没有配置水质监测点位"
		/>
	</view>
</template>

<script>
	import empyt from '@/components/controlPanel/controlPanel.vue'
	import EmptyData from '@/components/EmptyData/EmptyData.vue';
	import {
		getToday
	} from '@/utils/util.js';
	import {
		waterqualityConfiglable,
		waterqualityConfigData,
		waterqualityConfigEcharts
	} from '@/api/waterquality/waterquality.js'
	import {
		getProject
	} from "@/utils/auth.js";

	export default {
		components: {
			empyt,
			EmptyData
		},
		data() {
			return {
				loadmoreText: '暂无数据',
				iconColor: '#f5f5f5',
				iconShow: true,
				pickerShow: false,
				tabList: [],
				contentData: [],
				selectData: null,
				inactiveStyle: {
					color: '#646566'
				},
				activeStyle: {
					color: '#f5f5f5'
				},
				barStyle: {} ,
			}
		},

		onLoad() {
			this.fetchTabsData();
		},
		onShow() {
			this.restoreTabSelection();
		},
		methods: {
			async fetchTabsData() {
				try {
					this.project = getProject();
					const params = {
						data: {
							projectId: this.project.id,
							configType: 1
						}
					};
					const response = await waterqualityConfiglable(params);
					if (response.data.length > 0) {
						const data = response.data;
						this.tabList = data.map(item => ({
							name: item.name,
							id: item.id,
							models: item.models
						}));

						this.selectData = this.tabList[0];
					} else {
						this.tabList = [];
						this.contentData = [];
						this.selectData = null;
						console.log('未找到数据');
					}
					if (this.tabList.length > 0) {
						this.clickTab(this.selectData);
					}
				} catch (error) {
					console.error("Error fetching tabs data:", error);
				}
			},
			async clickTab(item) {
				this.selectData = item;
				try {
					const params = {
						data: item.id
					};

					const response = await waterqualityConfigData(params);
					const formatDate = (dateString) => {
						return dateString ? dateString.replace(' ', 'T') : null;
					}
					if (response.data.datas.length > 0) {
						this.contentData = response.data.datas;
						if (response.data.time) {	
							let a = new Date(formatDate(response.data.time))
							const year = a.getFullYear();
							const month = String(a.getMonth() + 1).padStart(2, '0');
							const day = String(a.getDate()).padStart(2, '0');
							this.loadmoreText = `${year}-${month}-${day}`;
						} else {
							this.loadmoreText = "";
						}
					}
				} catch (error) {
					console.error("Error fetching content data:", error);
				}
			},
			waterEchar(data) {
				const queryString = JSON.stringify(data);
				console.log(queryString);
				
				uni.navigateTo({
					url: `/waterquality/waterEchar/index?data=${queryString}`
				});
			},
			btnWriteWater() {
				const datDate = getToday();
				// 转时间戳
				const datTime = new Date(datDate).getTime();
				const tabListStr = JSON.stringify(this.selectData);
				uni.navigateTo({
					url: `/waterquality/writeWater/index?data=${datTime}&tabList=${tabListStr}`
				});
			},
			restoreTabSelection() {
				const storedTab = this.selectData;
				if (storedTab) {
					this.clickTab(storedTab);
				}
			}
		}
	}
</script>

<style scoped>
	.arr {
		position: relative;
		height: 100vh;
		overflow: hidden;
		background: linear-gradient(135deg, rgba(18, 34, 46, 0.95) 0%, rgba(18, 34, 46, 0.85) 100%);
	}

	/* 工业风格背景装饰 */
	.tech-lines {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		pointer-events: none;
		z-index: 0;
	}

	.line {
		position: absolute;
		background: linear-gradient(90deg, transparent, rgba(33, 148, 255, 0.1), transparent);
		height: 1px;
		width: 100%;
		animation: lineMove 3s linear infinite;
	}

	.line-1 { top: 20%; }
	.line-2 { top: 50%; }
	.line-3 { top: 80%; }

	@keyframes lineMove {
		0% { transform: translateX(-100%); }
		100% { transform: translateX(100%); }
	}

	.content {
		padding-top: 20rpx;
		position: relative;
		z-index: 1;
	}

	/* 标题区域 */
	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 20rpx 20rpx;
	}

	.title-box {
		display: flex;
		flex-direction: column;
		gap: 8rpx;
	}

	.title-text {
		font-size: 28rpx;
		color: #fff;
		font-weight: bold;
		text-shadow: 0 0 10rpx rgba(33, 148, 255, 0.5);
	}

	.title-line {
		width: 60rpx;
		height: 3rpx;
		background: linear-gradient(90deg, #2194FF, transparent);
		animation: lineGlow 2s ease-in-out infinite;
	}

	@keyframes lineGlow {
		0%, 100% { opacity: 0.5; }
		50% { opacity: 1; }
	}

	.data-count {
		padding: 4rpx 16rpx;
		background: rgba(33, 148, 255, 0.1);
		border: 1px solid rgba(33, 148, 255, 0.3);
		border-radius: 20rpx;
	}

	.count-text {
		font-size: 20rpx;
		color: #2194FF;
	}

	.content-tab {
		background-color: rgba(18, 34, 46, 0.5);
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
		text-align: center;
		border-radius: 10rpx;
		border: 1px solid rgba(33, 148, 255, 0.2);
		margin: 0 20rpx;
	}

	.content-data {
		margin: 20rpx;
		padding: 15rpx;
		background: rgba(18, 34, 46, 0.5);
		border-radius: 12rpx;
		border: 1px solid rgba(33, 148, 255, 0.2);
		width: calc(100% - 40rpx);
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
		gap: 16rpx;
	}

	.data-row {
		animation: slideIn 0.5s ease-out forwards;
		opacity: 0;
		transform: translateY(20rpx);
		width: 100%;
	}

	@keyframes slideIn {
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.data-card {
		position: relative;
		padding: 20rpx;
		background: rgba(33, 148, 255, 0.05);
		border-radius: 8rpx;
		transition: all 0.3s ease;
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;
		box-sizing: border-box;
	}

	.data-card:active {
		background: rgba(33, 148, 255, 0.1);
		transform: scale(0.98);
	}

	.card-corner {
		position: absolute;
		width: 10rpx;
		height: 10rpx;
		border: 1px solid rgba(33, 148, 255, 0.5);
	}

	.top-left {
		top: 0;
		left: 0;
		border-right: none;
		border-bottom: none;
	}

	.top-right {
		top: 0;
		right: 0;
		border-left: none;
		border-bottom: none;
	}

	.bottom-left {
		bottom: 0;
		left: 0;
		border-right: none;
		border-top: none;
	}

	.bottom-right {
		bottom: 0;
		right: 0;
		border-left: none;
		border-top: none;
	}

	.data-text {
		font-size: 28rpx;
		padding-left: 5rpx;
		font-family: MiSans;
		color: #fff;
		text-shadow: 0 0 10rpx rgba(33, 148, 255, 0.3);
		flex: 1;
		text-align: left;
		margin-right: 20rpx;
	}

	.data-unit {
		display: flex;
		align-items: center;
		gap: 8rpx;
		min-width: 120rpx;
	}

	.data-unit-value {
		color: #2194FF;
		font-family: 'Courier New', monospace;
		text-shadow: 0 0 10rpx rgba(33, 148, 255, 0.3);
		font-size: 30rpx;
		text-align: right;
	}

	.custom-span {
		font-size: 24rpx;
		color: rgba(153, 153, 153, 1);
		margin-left: 4rpx;
	}

	.btn {
		position: relative;
		width: 60%;
		margin: 0 auto;
		overflow: hidden;
	}

	.btn-content {
		color: rgb(255, 255, 255);
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		border-radius: 16rpx;
		background: linear-gradient(135deg, rgba(33, 148, 255, 0.2), rgba(33, 148, 255, 0.1));
		border: 1px solid rgba(33, 148, 255, 0.3);
		transition: all 0.3s ease;
	}

	.btn-border {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		border-radius: 16rpx;
		background: linear-gradient(90deg, transparent, rgba(33, 148, 255, 0.5), transparent);
		opacity: 0;
		transition: opacity 0.3s ease;
		pointer-events: none;
	}

	.btn:active .btn-content {
		transform: scale(0.98);
		background: linear-gradient(135deg, rgba(33, 148, 255, 0.3), rgba(33, 148, 255, 0.2));
	}

	.btn:active .btn-border {
		opacity: 1;
	}
</style>