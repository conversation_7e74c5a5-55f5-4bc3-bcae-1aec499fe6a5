<template>

	<view class="header-container" :style="{'marginTop': flex ? height + menuButtonInfo.height + 5 + 'px' : ''}">
		<view :class="{'flexs':flex}" :style="{height:height + 'px',background:bg}"></view>
		<view class="text-center flex justify-center align-center header-title" :class="{'flexs':flex}" :style="{top: computedTopStyle ,height:menuButtonInfo.height + 8 + 'px',lineHeight:menuButtonInfo.top + 'px',background:bg}">
			<view v-if="iconShow" class="flex header-icon" @tap="backPage">
				<u-icon :color="iconColor" size="45rpx" name="arrow-left"></u-icon>
			</view>
			<text class="header-title-text">{{title}}</text>
		</view>
	</view>

</template>

<script>
  export default {
    name: 'AppHeader',
    emits:['backPage'],
    props: {
      title: { // 标题
        type:String,
        default:'123'
      },
      // customStyle: { // 字体颜色
      //   type: Object,
      //   default:{
      //     'color':'#000',
      //     'font-size':'32rpx',
      //     'font-weight':'500'
      //   }
      // },
      iconColor:{//返回图标颜色
        type: String
      },
      iconShow:{
        type: Boolean, // 是否显示返回图标
        default:false
      },
      flex:{
        type: Boolean, // 是否固定在顶部
        default:false
      },
      bg:{
        type: String, // 背景颜色
      },
      backshow:{//是否自定义返回事件
        type:Boolean,
        default:false,
      }
    },
    data() {
      return {
        height: 0,
        menuButtonInfo: {
          height: 32,
          top: 0
        },
        computedTopStyle: ''
      }
    },
    created() {
      this.initHeaderInfo()
    },
    methods: {
      initHeaderInfo() {
        const systemInfo = uni.getSystemInfoSync();
        
        // 设置状态栏高度
        if (systemInfo && systemInfo.statusBarHeight) {
          this.height = systemInfo.statusBarHeight;
        } else {
          // APP端默认状态栏高度
          this.height = 20;
        }
        
        // 获取菜单按钮信息（仅小程序支持）
        //#ifdef MP-WEIXIN
        try {
          this.menuButtonInfo = uni.getMenuButtonBoundingClientRect();
        } catch (e) {
          console.warn('getMenuButtonBoundingClientRect not supported');
          this.menuButtonInfo = {
            height: 32,
            top: this.height
          };
        }
        //#endif
        
        //#ifdef APP-PLUS
        // APP端使用默认值
        this.menuButtonInfo = {
          height: 44, // APP端导航栏高度
          top: this.height
        };
        //#endif
        
        //#ifdef H5
        // H5端使用默认值
        this.menuButtonInfo = {
          height: 44,
          top: this.height
        };
        //#endif
        
        // 计算顶部样式
        this.computedTopStyle = this.flex ? this.height + 'px' : '';
      },
      backPage() {
        if (this.backshow) {
          this.$emit('backPage')
        } else {
          // 获取当前页面栈和当前页面信息
          const pages = getCurrentPages();
          const currentPage = pages[pages.length - 1];
          const currentRoute = currentPage?.route;
          
          console.log('返回按钮点击 - 当前页面栈信息:', {
            length: pages.length,
            currentPage: currentRoute,
            previousPage: pages.length > 1 ? pages[pages.length - 2]?.route : null,
            allPages: pages.map(p => p.route)
          });
          
          // 定义应用的主要页面结构
          const tabBarPages = [
            'pages/index',        // 首页
            'pages/kanban/index', // 看板页面  
            'pages/mine/index'    // 我的页面
          ];
          
          const isCurrentPageTabBar = tabBarPages.includes(currentRoute);
          
          // 如果当前页面是 tabBar 页面但不是首页，返回到首页
          if (isCurrentPageTabBar && currentRoute !== 'pages/index') {
            console.log('从tabBar页面返回首页:', currentRoute, '-> pages/index');
            uni.switchTab({
              url: '/pages/index',
              success: () => {
                console.log('成功返回首页');
              },
              fail: (err) => {
                console.error('返回首页失败:', err);
                // 兜底方案
                uni.reLaunch({
                  url: '/pages/index'
                });
              }
            });
            return;
          }
          
          // 如果当前页面是首页，显示退出应用确认
          if (currentRoute === 'pages/index') {
            console.log('当前在首页，显示退出确认');
            this.handleAppExit();
            return;
          }
          
          // 对于普通页面，根据页面栈情况决定返回方式
          if (pages.length > 1) {
            const previousPage = pages[pages.length - 2];
            console.log('普通页面返回:', {
              from: currentRoute,
              to: previousPage.route
            });
            
            // 使用 navigateBack 返回上一页
            uni.navigateBack({
              delta: 1,
              success: (res) => {
                console.log('✅ 返回上一页成功', res);
              },
              fail: (err) => {
                console.error('❌ navigateBack失败:', err);
                
                // 如果 navigateBack 失败，尝试其他返回方式
                const previousRoute = previousPage.route;
                
                // 如果上一页是 tabBar 页面，使用 switchTab
                if (tabBarPages.includes(previousRoute)) {
                  console.log('上一页是tabBar页面，使用switchTab返回');
                  uni.switchTab({
                    url: '/' + previousRoute,
                    success: () => {
                      console.log('switchTab返回成功');
                    },
                    fail: () => {
                      console.log('switchTab也失败，返回首页');
                      uni.switchTab({
                        url: '/pages/index'
                      });
                    }
                  });
                } else {
                  // 普通页面使用 redirectTo
                  console.log('使用redirectTo返回');
                  uni.redirectTo({
                    url: '/' + previousRoute,
                    success: () => {
                      console.log('redirectTo返回成功');
                    },
                    fail: () => {
                      console.log('redirectTo也失败，返回首页');
                      uni.switchTab({
                        url: '/pages/index'
                      });
                    }
                  });
                }
              }
            });
          } else {
            // 页面栈只有一页，但不是首页的情况（理论上不应该发生）
            console.log('页面栈异常，强制返回首页');
            uni.switchTab({
              url: '/pages/index'
            });
          }
        }
      },
      
      // 兜底导航方案
      fallbackNavigation() {
                //#ifdef APP-PLUS
                uni.reLaunch({
                  url: '/pages/index'
                });
                //#endif
                
                //#ifdef MP-WEIXIN
                uni.switchTab({
                  url: '/pages/index'
                });
                //#endif
                
                //#ifdef H5
                if (window.history.length > 1) {
                  window.history.back();
                } else {
                  uni.reLaunch({
                    url: '/pages/index'
                  });
                }
                //#endif
      },
      
      // 处理应用退出
      handleAppExit() {
            //#ifdef APP-PLUS
            uni.showModal({
              title: '提示',
              content: '确定要退出应用吗？',
              success: (res) => {
                if (res.confirm) {
                  //#ifdef APP-PLUS-ANDROID
                  plus.runtime.quit();
                  //#endif
                  
                  //#ifdef APP-PLUS-IOS
                  plus.runtime.launcher();
                  //#endif
                }
              }
            });
            //#endif
            
            //#ifdef MP-WEIXIN
        // 小程序不支持退出应用，可以考虑其他处理方式
        uni.showToast({
          title: '按下Home键退出',
          icon: 'none'
            });
            //#endif
            
            //#ifdef H5
        // H5可以考虑关闭窗口或跳转到首页
        if (window.history.length > 1) {
          window.history.back();
        } else {
          uni.showToast({
            title: '您已在首页',
            icon: 'none'
            });
        }
            //#endif
      }
    }
  }

</script>
<style scoped>
	.flexs{
		width: 100%;
		position: fixed;
		left: 0;
		top: 0;
		z-index: 100;
	}
  .header-container {
    position: relative;
    z-index: 100;
  }
  .header-title {
    position: fixed;
  }
  .header-icon {
    height: 100rpx;
    width: 100rpx;
    position: absolute;
    left: 20rpx;
  }

  /* 基于logo的渐变文字样式 */
  .header-title-text {
    font-size: 32rpx;
    font-weight: 1000;
    background: linear-gradient(135deg, #00BFFF 0%, #1E90FF 30%, #32CD32 70%, #00FF7F 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 0 20rpx rgba(30, 144, 255, 0.3);
    animation: titleGlow 3s ease-in-out infinite alternate;
  }

  @keyframes titleGlow {
    0% {
      filter: brightness(1) saturate(1);
      text-shadow: 0 0 20rpx rgba(30, 144, 255, 0.3);
    }
    100% {
      filter: brightness(1.1) saturate(1.2);
      text-shadow: 0 0 30rpx rgba(0, 255, 127, 0.4);
    }
  }
</style>
