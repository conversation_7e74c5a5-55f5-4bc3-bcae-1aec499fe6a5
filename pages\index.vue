<template>
	<view class="industrial-home">
		<AppHeader flex title="CIMS" :bg="bg" />

		<view class="status-bar">
			<view class="status-bar-decoration"></view>

			<!-- 项目名称居中显示 -->
			<view class="status-row-center">
				<view class="facility-info">
					<view class="facility-name" @click="handleBuilding('project')">
						<text class="project-name-text" :class="{ 'long-text': isLongProjectName }">{{ project.name ?
							project.name : '环际低碳' }}</text>
						<view class="facility-status-line"></view>
					</view>
				</view>
			</view>

	
			<view class="status-row-time">
				<view class="time-display">
					<text class="current-time">{{ currentTime }}</text>
				</view>
			</view>
		</view>

		<view class="arr">
			<!-- 数据概览面板 -->
			<view class="data-overview-panel">
				<view class="panel-header">
					<view class="header-circuit"></view>
					<text class="panel-title">实时监控概览</text>
					<view class="header-stats">
						<view class="stat-item">
							<text class="stat-value">{{ alarmCnt }}</text>
							<text class="stat-label">告警</text>
						</view>
						<view class="stat-item">
							<text class="stat-value">{{ unreadMsgdata }}</text>
							<text class="stat-label">消息</text>
						</view>
					</view>
				</view>
				<view class="panel-grid-lines">
					<view class="grid-line horizontal" v-for="i in 3" :key="'h' + i"></view>
					<view class="grid-line vertical" v-for="i in 4" :key="'v' + i"></view>
				</view>
			</view>

			<view class="industrial-grid">
				<!-- 主要监控模块 -->
				<view class="grid-section main-modules">
					<view class="section-header">
						<view class="header-circuit-left"></view>
						<view class="header-connector"></view>
						<view class="header-line-animated"></view>
						<view class="header-terminal"></view>
					</view>
					<view class="module-row">
						<view class="industrial-module primary enhanced" @click="navigateToPage('kanban')">
							<view class="module-frame">
								<view class="corner-decoration tl"></view>
								<view class="corner-decoration tr"></view>
								<view class="corner-decoration bl"></view>
								<view class="corner-decoration br"></view>
							</view>
							<view class="module-header">
								<view class="module-indicator active">
									<view class="indicator-center"></view>
									<view class="indicator-orbit"></view>
								</view>
							</view>
							<view class="module-content">
								<view class="module-icon-container">
									<view class="icon-backdrop"></view>
									<image class="module-icon" src="/static/images/tabs/zbkb.png" mode="aspectFit" />
									<view class="icon-glow primary"></view>
									<view class="icon-scan-line"></view>
								</view>
								<view class="module-info">
									<text class="module-title">指标看板</text>
									<text class="module-desc">实时监控关键指标</text>
									<view class="module-metrics">
										<view class="metric-bar">
											<view class="bar-fill" style="width: 85%"></view>
										</view>
									</view>
								</view>
							</view>
							<view class="module-footer">
								<view class="connection-line primary">
									<view class="line-pulse"></view>
								</view>
							</view>
						</view>

						<view class="industrial-module secondary enhanced" @click="navigateToPage('dataMonitor')">
							<view class="module-frame">
								<view class="corner-decoration tl"></view>
								<view class="corner-decoration tr"></view>
								<view class="corner-decoration bl"></view>
								<view class="corner-decoration br"></view>
							</view>
							<view class="module-header">
								<view class="module-indicator active">
									<view class="indicator-center"></view>
									<view class="indicator-orbit"></view>
								</view>
							</view>
							<view class="module-content">
								<view class="module-icon-container">
									<view class="icon-backdrop"></view>
									<image class="module-icon" src="/static/images/tabs/jk.png" mode="aspectFit" />
									<view class="icon-glow secondary"></view>
									<view class="icon-scan-line"></view>
								</view>
								<view class="module-info">
									<text class="module-title">数据监控</text>
									<text class="module-desc">设备状态监控</text>
									<!-- <view class="monitor-display">
										<view class="data-stream">
											<view class="stream-line"></view>
											<view class="stream-dots">
												<view class="dot" v-for="i in 3" :key="i"></view>
											</view>
										</view>
										<text class="monitor-status">实时监控中...</text>
									</view> -->
								</view>
							</view>
							<view class="module-footer">
								<view class="connection-line secondary">
									<view class="line-pulse"></view>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 告警与通信模块 -->
				<view class="grid-section alert-modules">
					<view class="section-header">
						<view class="header-circuit-left alert"></view>
						<view class="header-connector alert"></view>
						<view class="header-line-animated alert"></view>
						<view class="header-terminal alert"></view>
					</view>
					<view class="module-row">
						<view class="industrial-module alert enhanced" @click="navigateToPage('alarm')">
							<view class="module-frame alert">
								<view class="corner-decoration tl"></view>
								<view class="corner-decoration tr"></view>
								<view class="corner-decoration bl"></view>
								<view class="corner-decoration br"></view>
							</view>
							<view class="module-header">
								<view class="module-indicator" :class="alarmCnt > 0 ? 'alert' : 'normal'">
									<view class="indicator-center"></view>
									<view class="indicator-orbit" :class="alarmCnt > 0 ? 'alert' : ''"></view>
								</view>
								<view class="alert-badge" v-if="alarmCnt > 0">
									<text>{{ alarmCnt > 99 ? '99+' : alarmCnt }}</text>
									<view class="badge-glow"></view>
								</view>
							</view>
							<view class="module-content">
								<view class="module-icon-container">
									<view class="icon-backdrop alert"></view>
									<image class="module-icon" src="/static/images/tabs/warn.png" mode="aspectFit" />
									<view class="icon-glow alert" v-if="alarmCnt > 0"></view>
									<view class="icon-scan-line" :class="alarmCnt > 0 ? 'alert' : ''"></view>
								</view>
								<view class="module-info">
									<text class="module-title">告警中心</text>
									<text class="module-desc">系统告警管理</text>
									<view class="module-metrics">
										<view class="alert-status-display">
											<text class="status-text" v-if="alarmCnt === 0">系统正常运行</text>
											<text class="status-text alert" v-else>{{ alarmCnt }}个未处理告警</text>
											<view class="alert-level-indicator">
												<view class="level-bar critical" :class="alarmCnt > 5 ? 'active' : ''">
												</view>
												<view class="level-bar warning" :class="alarmCnt > 2 ? 'active' : ''">
												</view>
												<view class="level-bar normal" :class="alarmCnt > 0 ? 'active' : ''">
												</view>
											</view>
										</view>
									</view>
								</view>
							</view>
							<view class="module-footer">
								<view class="connection-line" :class="alarmCnt > 0 ? 'alert' : 'normal'">
									<view class="line-pulse" :class="alarmCnt > 0 ? 'alert' : ''"></view>
								</view>
							</view>
						</view>

						<view class="industrial-module communication enhanced" @click="navigateToPage('messageCenter')">
							<view class="module-frame communication">
								<view class="corner-decoration tl"></view>
								<view class="corner-decoration tr"></view>
								<view class="corner-decoration bl"></view>
								<view class="corner-decoration br"></view>
							</view>
							<view class="module-header">
								<view class="module-indicator" :class="unreadMsgdata > 0 ? 'message' : 'normal'">
									<view class="indicator-center"></view>
									<view class="indicator-orbit" :class="unreadMsgdata > 0 ? 'message' : ''"></view>
								</view>
								<view class="message-badge" v-if="unreadMsgdata > 0">
									<text>{{ unreadMsgdata > 99 ? '99+' : unreadMsgdata }}</text>
									<view class="badge-glow"></view>
								</view>
							</view>
							<view class="module-content">
								<view class="module-icon-container">
									<view class="icon-backdrop communication"></view>
									<image class="module-icon" src="/static/images/tabs/msg.png" mode="aspectFit" />
									<view class="icon-glow message" v-if="unreadMsgdata > 0"></view>
									<view class="icon-scan-line" :class="unreadMsgdata > 0 ? 'message' : ''"></view>
								</view>
								<view class="module-info">
									<text class="module-title">消息中心</text>
									<text class="module-desc">通信消息管理</text>
									<view class="module-metrics">
										<view class="signal-bars">
											<view class="signal-bar" v-for="i in 4" :key="i" :class="'bar-' + i"></view>
										</view>
									</view>
								</view>
							</view>
							<view class="module-footer">
								<view class="connection-line" :class="unreadMsgdata > 0 ? 'message' : 'normal'">
									<view class="line-pulse" :class="unreadMsgdata > 0 ? 'message' : ''"></view>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 分析与维护模块 -->
				<view class="grid-section analysis-modules">
					<view class="section-header">
						<view class="header-circuit-left analysis"></view>
						<view class="header-connector analysis"></view>
						<view class="header-line-animated analysis"></view>
						<view class="header-terminal analysis"></view>
					</view>
					<view class="module-grid">
						<view class="industrial-module compact enhanced" @click="navigateToPage('waterquality')">
							<view class="compact-frame">
								<view class="corner-mini tl"></view>
								<view class="corner-mini tr"></view>
								<view class="corner-mini bl"></view>
								<view class="corner-mini br"></view>
							</view>
							<view class="compact-header">
								<view class="compact-indicator normal">
									<view class="mini-pulse"></view>
								</view>
								<text class="compact-title">水质报告</text>
							</view>
							<view class="compact-content">
								<view class="compact-icon-wrapper">
									<image class="compact-icon" src="/static/images/tabs/water.png" mode="aspectFit" />
									<view class="compact-glow"></view>
								</view>
								<view class="compact-metrics">
									<view class="water-analysis">
										<view class="wave-line"></view>
										<view class="quality-dots">
											<view class="dot" v-for="i in 3" :key="i"></view>
										</view>
									</view>
									<text class="status-text">检测中...</text>
								</view>
							</view>
						</view>

						<view class="industrial-module compact enhanced" @click="navigateToPage('diagram')">
							<view class="compact-frame">
								<view class="corner-mini tl"></view>
								<view class="corner-mini tr"></view>
								<view class="corner-mini bl"></view>
								<view class="corner-mini br"></view>
							</view>
							<view class="compact-header">
								<view class="compact-indicator normal">
									<view class="mini-pulse"></view>
								</view>
								<text class="compact-title">图表分析</text>
							</view>
							<view class="compact-content">
								<view class="compact-icon-wrapper">
									<image class="compact-icon" src="/static/images/tabs/echarts.png"
										mode="aspectFit" />
									<view class="compact-glow"></view>
								</view>
								<view class="compact-metrics">
									<view class="chart-animation">
										<view class="data-flow-line"></view>
										<view class="chart-points">
											<view class="point" v-for="i in 4" :key="i"></view>
										</view>
									</view>
									<text class="status-text">分析中...</text>
								</view>
							</view>
						</view>

						<view class="industrial-module compact enhanced" @click="navigateToPage('analysis')">
							<view class="compact-frame">
								<view class="corner-mini tl"></view>
								<view class="corner-mini tr"></view>
								<view class="corner-mini bl"></view>
								<view class="corner-mini br"></view>
							</view>
							<view class="compact-header">
								<view class="compact-indicator normal">
									<view class="mini-pulse"></view>
								</view>
								<text class="compact-title">智能运维</text>
							</view>
							<view class="compact-content">
								<view class="compact-icon-wrapper">
									<image class="compact-icon" src="/static/images/tabs/znyw.png" mode="aspectFit" />
									<view class="compact-glow"></view>
								</view>
								<view class="compact-metrics">
									<view class="ai-processing">
										<view class="neural-line"></view>
										<view class="ai-nodes">
											<view class="node" v-for="i in 3" :key="i"></view>
										</view>
									</view>
									<text class="status-text">处理中...</text>
								</view>
							</view>
						</view>

						<view class="industrial-module compact enhanced" @click="navigateToPage('mine')">
							<view class="compact-frame">
								<view class="corner-mini tl"></view>
								<view class="corner-mini tr"></view>
								<view class="corner-mini bl"></view>
								<view class="corner-mini br"></view>
							</view>
							<view class="compact-header">
								<view class="compact-indicator normal">
									<view class="mini-pulse"></view>
								</view>
								<text class="compact-title">系统设置</text>
							</view>
							<view class="compact-content">
								<view class="compact-icon-wrapper">
									<image class="compact-icon" src="/static/images/tabs/mine.png" mode="aspectFit" />
									<view class="compact-glow"></view>
								</view>
								<view class="compact-metrics">
									<view class="config-sync">
										<view class="sync-line"></view>
										<view class="config-dots">
											<view class="dot" v-for="i in 3" :key="i"></view>
										</view>
									</view>
									<text class="status-text">配置中...</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<view v-for="(i, y) in homeOperationsData" :key="y">
				<h3 style="color: #fff;margin: 10rpx 0 10rpx 30rpx;">{{ i.powerUnitName }}</h3>
				<view class="suggestion" v-if="i.suggestionListstate">
					<view class="conclusion_title">
						<view class="title-text">建议</view>
					</view>
					<view>
						<view class="container">
							<view class="backPressure">
								<view class="backPressureImg">
									<image class="backPressureIocn" src="/static/images/tabs/by.png" mode="aspectFit" />
								</view>
								<view class="backPressureText">
									<view class="backPressureTitle">
										{{ i.suggestionList[0].name === undefined ? "--" : i.suggestionList[0].name }}</view>
									<view class="backPressureValue">
										{{ i.suggestionList[0].value === "" ? '--' : i.suggestionList[0].value }}
										{{ i.suggestionList[0].value === "" || i.suggestionList[0].unit === null ? '' : i.suggestionList[0].unit }}
									</view>
								</view>
							</view>
						</view>
						<view class="pumpAndfan">
							<view class="pump">
								<view class="pumpImg">
									<image class="pumpIocn" src="/static/images/tabs/beng.png" mode="aspectFit" />
								</view>
								<view class="pumpText">
									<view class="pumpTitle" style="margin-bottom: 10rpx;color: rgba(204, 204, 204, 1);">
										{{ i.suggestionList[1].name === undefined ? "--" : i.suggestionList[1].name }}
									</view>
									<view class="pumpValue">
										{{ i.suggestionList[1].value === "" ? '--' : i.suggestionList[1].value }}
										{{ i.suggestionList[1].value === "" || i.suggestionList[1].unit === null ? '' : i.suggestionList[1].unit }}
									</view>
								</view>
							</view>
							<view class="fan">
								<view class="pumpImg">
									<image class="pumpIocn" src="/static/images/tabs/fen.png" mode="aspectFit" />
								</view>
								<view class="pumpText">
									<view class="pumpTitle" style="margin-bottom: 10rpx;color: rgba(204, 204, 204, 1);">
										{{ i.suggestionList[2].name === undefined ? "--" : i.suggestionList[2].name }}
									</view>
									<view class="pumpValue">
										{{ i.suggestionList[2].value === undefined ? '--' : i.suggestionList[2].value }}
										{{ i.suggestionList[2].value === undefined || i.suggestionList[2].unit === null ? '' : i.suggestionList[2].unit }}
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
				<!-- 结论 -->
				<view class="conclusion" v-if="i.conclusionstate">
					<view class="conclusion_title">
						<view class="title-text">结论</view>
					</view>
					<uni-collapse v-model="collapseValue" :accordion="false">
						<uni-collapse-item v-for="(item, index) in i.conclusionList" :key="index"
							:name="index.toString()" style="margin-bottom: 5rpx;">
							<template v-slot:title>
								<view class="custom-title">
									<view class="custom-left">{{ item.type }} ({{ item.suggestedValue }})</view>
									<view class="custom-right">
										<view class="custom-right-value">{{
											item.currentValue === null ? '--' : item.currentValue }}</view>
										<view class="custom-right-state" :class="item.stateClass">
											<span>{{ item.stateText }}</span>
										</view>
									</view>
								</view>
							</template>
							<view class="custom-title-content">
								<view class="parameter-visualizer">
									<!-- 参数范围可视化滑块 -->
									<view class="range-slider-container">
										<view class="range-track">
											<!-- 安全范围背景 -->
											<view v-if="item.showBothCircles" class="safe-range"
												:style="{ left: '20%', width: '60%' }"></view>
											<view v-else-if="item.showMinCircle" class="safe-range-min"
												:style="{ left: '20%', width: '80%' }">
											</view>
											<view v-else-if="item.showMaxCircle" class="safe-range-max"
												:style="{ left: '0%', width: '80%' }">
											</view>

											<!-- 范围标记点 -->
											<view v-if="item.showBothCircles" class="range-markers">
												<view class="marker marker-min">
													<view class="marker-dot"></view>
													<text class="marker-label">{{ item.min }}</text>
												</view>
												<view class="marker marker-max">
													<view class="marker-dot"></view>
													<text class="marker-label">{{ item.max }}</text>
												</view>
											</view>
											<view v-else-if="item.showMinCircle" class="range-markers">
												<view class="marker marker-min">
													<view class="marker-dot"></view>
													<text class="marker-label">{{ item.min }}</text>
												</view>
											</view>
											<view v-else-if="item.showMaxCircle" class="range-markers">
												<view class="marker marker-max">
													<view class="marker-dot"></view>
													<text class="marker-label">{{ item.max }}</text>
												</view>
											</view>

											<!-- 当前值指示器 -->
											<view class="current-indicator"
												:style="{ left: item.imagePositionPercentage + '%' }"
												:class="{ 'indicator-safe': item.stateClass === 'safe', 'indicator-warning': item.stateClass === 'warning', 'indicator-danger': item.stateClass === 'danger' }">
												<view class="indicator-pulse"></view>
												<view class="indicator-arrow"></view>
												<text class="indicator-value">{{ item.currentValue }}</text>
											</view>
										</view>
									</view>

									<!-- 状态信息面板 -->
									<view class="status-panel">
										<view class="status-row">
											<view class="status-item">
												<text class="status-label">类型</text>
												<text class="status-value">{{ item.type }}</text>
											</view>
											<view class="status-item">
												<text class="status-label">建议值</text>
												<text class="status-value suggested">{{ item.suggestedValue }}</text>
											</view>
											<view class="status-item">
												<text class="status-label">当前值</text>
												<text class="status-value current" :class="item.stateClass">{{
													item.currentValue || '--' }}</text>
											</view>
										</view>
										<view class="status-indicator" :class="item.stateClass">
											<view class="indicator-light"></view>
											<!-- <text class="status-text">{{ item.stateText }}</text> -->
										</view>
									</view>
								</view>
								<view class="conclusion-panel">
									<view class="conclusion-header">
										<text class="conclusion-title">分析结论</text>
										<view class="conclusion-line"></view>
									</view>
									<text class="conclusion-text">{{ item.conclusion }}</text>
								</view>
							</view>
						</uni-collapse-item>
					</uni-collapse>
				</view>
			</view>

		</view>
	</view>
</template>

<script>
import { getToken, getProject } from '@/utils/auth'
import errorCode from '../utils/errorCode';
import {
	toast,
	showConfirm,
	tansParams
} from '@/utils/common';
import {
	alarmLevel,
	alarmDict,
	unreadMsg
} from '@/api/alarm/alarm.js';
import {
	getIndicatorAnalysis
} from '@/api/analysis/analysis.js';
import config from '@/config.js';

export default {
	name: 'index',
	data() {
		return {
			suggestionListstate: false,
			conclusionstate: false,
			projectId: '',
			homeOperationsData: [],
			unreadMsgdata: 0,
			alarmCnt: 0,
			currentTime: '',
			project: {}, // 项目信息
			bg: '#131b2e',
			iconColor: '#ffffff',
			columnChartData: {},
			PancakeData: {},
			dictType: [],
			start: '',
			end: '',
			// 系统信息
			systemInfo: {
				name: config.appInfo.name,
				version: config.appInfo.systemVersion,
				status: 'LOADING...',
				platform: '',
				isOnline: false
			},
			systemUptime: '0天0时0分',
			systemStartTime: Date.now(), // 系统启动时间
			collapseValue: ['0'] // 默认展开第一个项目，但可以自由收起
		};
	},
	computed: {
		// 判断项目名称是否过长
		isLongProjectName() {
			const projectName = this.project.name || '环际低碳';
			// 如果超过12个字符，认为是长文本，需要缩小字体
			return projectName.length > 12;
		}
	},
	created() {
		this.getAlarmDict();
	},
	onShow() {
		const token = getToken();
		if (!token) {
			// 如果没有 token，跳转到登录页面
			uni.reLaunch({
				url: '/pages/login/index'
			});
			return;
		}
		this.getanalysisTime();
		this.project = getProject();
		this.updateCurrentTime();
		this.getSystemInfo(); // 获取系统信息
		this.getAlarmLevel();
		this.getunreadMsg();
		this.gethomeOperationsList();
	},
	onPullDownRefresh() {
		console.log('首页下拉刷新触发');
		// 重置告警计数
		this.alarmCnt = 0;
		this.unreadMsgdata = 0;
		this.homeOperationsData = [];

		// 刷新数据
		this.getanalysisTime();

		// 使用Promise.all确保所有请求完成后再停止刷新
		Promise.all([
			new Promise(resolve => {
				this.getAlarmLevel();
				resolve();
			}),
			new Promise(resolve => {
				this.getunreadMsg();
				resolve();
			}),
			new Promise(resolve => {
				this.gethomeOperationsList();
				resolve();
			})
		]).finally(() => {
			setTimeout(() => {
				uni.stopPullDownRefresh();
			}, 300);
		});
	},
	methods: {
		updateCurrentTime() {
			const now = new Date();
			const year = now.getFullYear();
			const month = String(now.getMonth() + 1).padStart(2, '0');
			const day = String(now.getDate()).padStart(2, '0');
			const hour = String(now.getHours()).padStart(2, '0');
			const minute = String(now.getMinutes()).padStart(2, '0');
			const second = String(now.getSeconds()).padStart(2, '0');

			this.currentTime = `${year}-${month}-${day} ${hour}:${minute}:${second}`;

			// 计算系统运行时间
			this.updateSystemUptime();

			// 每秒更新一次时间
			setTimeout(() => {
				this.updateCurrentTime();
			}, 1000);
		},
		updateSystemUptime() {
			const now = Date.now();
			const diff = now - this.systemStartTime;

			const days = Math.floor(diff / (1000 * 60 * 60 * 24));
			const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
			const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

			this.systemUptime = `${days}天${hours}时${minutes}分`;
		},
		// 获取系统状态信息
		getSystemInfo() {
			try {
				// 获取系统基本信息
				const systemInfo = uni.getSystemInfoSync();

				this.systemInfo.name = config.appInfo.name;
				this.systemInfo.version = config.appInfo.systemVersion;
				this.systemInfo.platform = systemInfo.platform;


				uni.getNetworkType({
					success: (res) => {
						if (res.networkType === 'none') {
							this.systemInfo.status = 'OFFLINE';
							this.systemInfo.isOnline = false;
						} else {
							this.systemInfo.status = 'ONLINE';
							this.systemInfo.isOnline = true;
						}
					},
					fail: () => {
						this.systemInfo.status = 'UNKNOWN';
						this.systemInfo.isOnline = false;
					}
				});

				console.log('系统信息更新完成');
			} catch (e) {
				console.log('获取系统信息失败:', e);
				this.systemInfo.status = 'ERROR';
			}
		},
		getanalysisTime() {
			const now = new Date();
			const year = now.getFullYear();
			const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
			const day = String(now.getDate()).padStart(2, '0');
			const hour = String(now.getHours()).padStart(2, '0');
			const minute = now.getMinutes();

			let startTime, endTime;

			if (minute >= 30) {
				const startMinute = '30';
				const startSecond = '00';
				startTime = `${year}-${month}-${day} ${hour}:${startMinute}:${startSecond}`;

				// 结束时间为当前小时的59分59秒
				const endMinute = '59';
				const endSecond = '59';
				endTime = `${year}-${month}-${day} ${hour}:${endMinute}:${endSecond}`;
			} else {
				// 开始时间为当前小时的00分00秒
				const startMinute = '00';
				const startSecond = '00';
				startTime = `${year}-${month}-${day} ${hour}:${startMinute}:${startSecond}`;

				// 结束时间为当前小时的29分59秒
				const endMinute = '29';
				const endSecond = '59';
				endTime = `${year}-${month}-${day} ${hour}:${endMinute}:${endSecond}`;
			}

			this.start = startTime;
			this.end = endTime;
		},
		gethomeOperationsList() {
			const params = {
				data: {
					projectId: this.project.id,
					configType: 7,
					start: this.start,
					end: this.end
				}
			};
			getIndicatorAnalysis(params)
				.then((res) => {
					if (res.code === 50002001) {
						this.homeOperationsData = [];
						return;
					} else {
						if (res.data) {
							this.homeOperationsData = res.data;
							this.homeOperationsData.forEach(item => {
								if (item.suggestionList != null) {
									item.suggestionListstate = true;
									// this.suggestionListstate = true;
								}
								if (item.conclusionList != null) {
									item.conclusionstate = true;
									// this.conclusionstate = true;
								}
							})
							this.updateConclusionList(); // 更新结论列表
						} else {
							this.homeOperationsData = {};
						}
					}
				})
				.catch((res) => {
					uni.showToast({
						title: '请求失败，请稍后再试',
						icon: 'none'
					});
					this.homeOperationsData = {};
				})
		},
		getunreadMsg() {
			const projectId = this.project.id;
			const params = {
				data: projectId
			};
			unreadMsg(params).then(res => {
				if (res.code === 200) {
					this.unreadMsgdata = res.data;
				} else {
					this.unreadMsgdata = 0;
				}
			}).catch((res) => {
				this.unreadMsgdata = 0
			});
		},
		handleBuilding(pageName) {
			switch (pageName) {
				case 'project':
					uni.navigateTo({
						url: "/projectManagement/index"
					});
					break;
			}
		},
		// 获取告警等级
		getAlarmDict() {
			const params = {
				data: 'alert_level',
				pageNum: 1,
				pageSize: 30
			};
			alarmDict(params).then(res => {
				if (res.code === 401) {
					uni.navigateTo({
						url: '/pages/login/login'
					})
					return
				}
				if (res.code === 200) {
					if (res.data.length > 0) {
						const filteredData = res.data.filter(item => item.dictValue !== "-1");
						this.dictType = filteredData.map(item => {
							return {
								text: item.dictLabel,
								value: item.dictValue
							};
						});
					}
				}

			});
		},
		getAlarmLevel() {
			const projectId = this.project.id;
			const params = {
				data: projectId
			};

			// 重置告警计数，避免重复累加
			this.alarmCnt = 0;

			alarmLevel(params).then(res => {
				if (res.data.length > 0) {
					const data = res.data.map(item => {
						const dictItem = this.dictType.find(dict => dict.value == item.level);
						this.alarmCnt += item.cnt; // 累加cnt值
						return {
							name: dictItem ? dictItem.text : '',
							value: item.cnt
						};
					});
				} else {
					this.PancakeData = [];
				}
			}).catch(res => {
				this.PancakeData = [];
			});
		},
		navigateToPage(pageName) {
			switch (pageName) {
				case 'kanban':
					uni.switchTab({
						url: '/pages/kanban/index'
					});
					break;
				case 'waterquality':
					uni.navigateTo({
						url: '/waterquality/index'
					});
					break;
				case 'diagram':
					uni.navigateTo({
						url: '/diagram/diagram'
					});
					break;
				case 'alarm':
					uni.navigateTo({
						url: '/alarm/index'
					});
					break;
				case 'mine':
					uni.switchTab({
						url: '/pages/mine/index'
					});
					break;
				case 'dataMonitor':
					uni.navigateTo({
						url: '/dataMonitor/index'
					});
					break;
				case 'analysis':
					uni.navigateTo({
						url: '/analysis/index'
					});
					break;
				case 'messageCenter':
					uni.navigateTo({
						url: '/messageCenter/index',
					});
					break;
			}
		},
		// 更新结论列表
		updateConclusionList() {
			if (this.homeOperationsData.length > 0) {
				this.homeOperationsData.forEach(i => {
					if (i.conclusionList && i.conclusionList.length > 0) {
						i.conclusionList.forEach(item => {
							// 设置状态类名和状态文本
							switch (item.dataStatus) {
								case '1':
									item.stateClass = 'state-high';
									item.stateText = '偏高';
									break;
								case '2':
									item.stateClass = 'state-normal';
									item.stateText = '正常';
									break;
								case '3':
									item.stateClass = 'state-low';
									item.stateText = '偏低';
									break;
								case null:
									item.stateClass = 'state-high';
									item.stateText = '异常';
									break;
								// default:
								// 	item.stateClass = 'state-high';
								// 	item.stateText = '异常';
							}

							// 设置显示圆圈的标识
							if (item.min !== null && item.max !== null) {
								item.showBothCircles = true;
								item.showMinCircle = false;
								item.showMaxCircle = false;
							} else if (item.min !== null && item.max === null) {
								item.showBothCircles = false;
								item.showMinCircle = true;
								item.showMaxCircle = false;
							} else if (item.min === null && item.max !== null) {
								item.showBothCircles = false;
								item.showMinCircle = false;
								item.showMaxCircle = true;
							} else {
								item.showBothCircles = false;
								item.showMinCircle = false;
								item.showMaxCircle = false;
							}

							// 计算图片位置（基于百分比）
							item.imagePositionPercentage = this.calculateImagePositionPercentage(item.currentValue,
								item.min, item.max);
						});
					}
				})
			}


		},
		// 计算图片位置的方法，返回百分比
		calculateImagePositionPercentage(currentValue, min, max) {
			let position = 50; // 默认居中百分比

			if (min !== null && max !== null) {
				// 当 min 和 max 都存在时
				const range = max - min;
				const totalShares = 3 * range;

				if (range === 0) {
					position = 50;
				} else {
					let positionShares = 0;

					if (currentValue < min) {
						positionShares = currentValue - (min - range);
					} else if (currentValue >= min && currentValue <= max) {
						positionShares = (currentValue - min) + range;
					} else { // currentValue > max
						positionShares = (currentValue - max) + 2 * range;
					}

					position = (positionShares / totalShares) * 100;
				}
			} else if (min !== null && max === null) {
				// 只有 min 存在时
				const range = min;
				const totalShares = 2 * range;

				let positionShares = currentValue;

				if (currentValue < 0) {
					positionShares = 0;
				} else if (currentValue >= 0 && currentValue <= 2 * min) {
					positionShares = currentValue;
				} else { // currentValue > 2 * min
					positionShares = 2 * min;
				}

				position = (positionShares / totalShares) * 100;
			} else if (min === null && max !== null) {
				// 只有 max 存在时
				const range = max;
				const totalShares = 2 * range;

				let positionShares = currentValue;

				if (currentValue < 0) {
					positionShares = 0;
				} else if (currentValue >= 0 && currentValue <= 2 * max) {
					positionShares = currentValue;
				} else { // currentValue > 2 * max
					positionShares = 2 * max;
				}

				position = (positionShares / totalShares) * 100;
			} else {
				// 既没有 min 也没有 max，默认居中
				position = 50;
			}

			position = Math.max(0, Math.min(position, 100));

			return position;
		}
	},
};
</script>

<style lang="scss" scoped>
.industrial-home {
	background: linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 50%, #0f1419 100%);
	min-height: 100vh;
	position: relative;
	overflow-x: hidden;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-image:
			radial-gradient(circle at 25% 25%, rgba(33, 148, 255, 0.05) 0%, transparent 50%),
			radial-gradient(circle at 75% 75%, rgba(0, 255, 136, 0.03) 0%, transparent 50%);
		pointer-events: none;
	}
}

.status-bar {
	background: linear-gradient(135deg, #131b2e 0%, #1e2a3a 100%);
	border-bottom: 1rpx solid #2a3441;
	padding: 25rpx 30rpx 50rpx 30rpx;
	position: relative;
	overflow: hidden;
	min-height: 140rpx;

	.status-bar-decoration {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 2rpx;
		background: linear-gradient(90deg,
				transparent 0%,
				#2194FF 20%,
				#00ff88 50%,
				#2194FF 80%,
				transparent 100%);
		animation: statusPulse 3s ease-in-out infinite;
	}

	&::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		height: 1rpx;
		background: linear-gradient(90deg, transparent 0%, #2194FF 50%, transparent 100%);
	}
}

@keyframes statusPulse {

	0%,
	100% {
		opacity: 0.6;
	}

	50% {
		opacity: 1;
	}
}



/* 简洁的在线状态 */
.online-status {
	display: flex;
	align-items: center;
	gap: 8rpx;
	margin-top: 8rpx;
}

.status-dot {
	width: 8rpx;
	height: 8rpx;
	border-radius: 50%;
	background: #00ff88;
	box-shadow: 0 0 8rpx rgba(0, 255, 136, 0.5);
	animation: onlinePulse 2s ease-in-out infinite;
}

.status-text {
	font-size: 22rpx;
	color: rgba(255, 255, 255, 0.8);
}

@keyframes onlinePulse {

	0%,
	100% {
		opacity: 1;
	}

	50% {
		opacity: 0.6;
	}
}

/* 项目名称居中显示，稍微向上偏移 */
.status-row-center {
	position: absolute;
	top: 40%; /* 向上偏移，为时间留出空间 */
	left: 50%;
	transform: translate(-50%, -50%);
	z-index: 2;
	width: 100%;
	text-align: center;
}

/* 时间显示在右下角，确保不与项目名称并排 */
.status-row-time {
	position: absolute;
	bottom: 15rpx;
	right: 30rpx;
	z-index: 2;
}

.facility-info {
	width: 100%;

	.facility-name {
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		font-size: 32rpx; /* 字体稍微大一号 */
		color: #ffffff;
		font-weight: 600;

		.project-name-text {
			text-align: center;
			line-height: 1.4;
			word-break: break-all;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 2; /* 最多显示两行 */
			line-clamp: 2;
			overflow: hidden;
			transition: font-size 0.3s ease;
			max-width: 500rpx; /* 增加最大宽度 */
		}

		.project-name-text.long-text {
			font-size: 30rpx; /* 长文本时稍微小一点 */
			-webkit-line-clamp: 2; /* 保持两行 */
			line-clamp: 2;
		}

		.facility-status-line {
			position: absolute;
			right: -60rpx; /* 调整到右侧更远的位置 */
			top: 50%;
			transform: translateY(-50%);
			width: 50rpx; /* 增加横线长度 */
			height: 2rpx; /* 稍微加粗 */
			background: linear-gradient(90deg, #2194FF 0%, transparent 100%);
		}
	}
}

@keyframes indicatorPulse {
	0% {
		transform: translate(-50%, -50%) scale(1);
		opacity: 1;
	}

	100% {
		transform: translate(-50%, -50%) scale(2);
		opacity: 0;
	}
}

.system-status {
	display: flex;
	align-items: center;
	gap: 16rpx;
	font-size: 24rpx;
	color: #a0a0a0;

	.status-dot {
		position: relative;
		width: 12rpx;
		height: 12rpx;
		flex-shrink: 0;

		&.running {
			.dot-core {
				width: 8rpx;
				height: 8rpx;
				background: #00ff88;
				border-radius: 50%;
				position: relative;
				z-index: 2;
			}

			.dot-ring {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				width: 16rpx;
				height: 16rpx;
				border: 1rpx solid #00ff88;
				border-radius: 50%;
				animation: dotRing 2s ease-in-out infinite;
			}
		}
	}

	.status-info {
		display: flex;
		flex-direction: column;
		gap: 8rpx;

		.status-text {
			font-size: 24rpx;
			color: #ffffff;
			font-weight: 500;
		}

		.status-indicator-line {
			width: 60rpx;
			height: 2rpx;
			background: linear-gradient(90deg, #00ff88 0%, transparent 100%);
			position: relative;

			&::after {
				content: '';
				position: absolute;
				right: 0;
				top: 50%;
				transform: translateY(-50%);
				width: 4rpx;
				height: 4rpx;
				background: #00ff88;
				border-radius: 50%;
				box-shadow: 0 0 6rpx #00ff88;
				animation: statusLinePulse 2s ease-in-out infinite;
			}
		}

		.uptime-text {
			font-size: 20rpx;
			color: #666;
			font-family: 'Courier New', monospace;
		}
	}
}

@keyframes dotRing {
	0% {
		transform: translate(-50%, -50%) scale(1);
		opacity: 1;
	}

	100% {
		transform: translate(-50%, -50%) scale(1.5);
		opacity: 0;
	}
}

.time-display {
	.current-time {
		font-size: 28rpx; /* 保持原有字体大小 */
		color: #2194FF;
		font-family: 'Courier New', monospace;
		font-weight: 500;
		text-shadow: 0 0 10rpx rgba(33, 148, 255, 0.3);
		text-align: right;
	}
}

.arr {
	background: #0a0e1a;
	min-height: calc(100vh - 120rpx);
	padding: 30rpx 20rpx 100rpx;
	position: relative;
}

/* 数据概览面板 */
.data-overview-panel {
	background: linear-gradient(135deg, #1a2332 0%, #0f1419 100%);
	border: 1rpx solid #2a3441;
	border-radius: 16rpx;
	margin-bottom: 40rpx;
	position: relative;
	overflow: hidden;

	.panel-header {
		padding: 24rpx 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-bottom: 1rpx solid #2a3441;

		.header-circuit {
			width: 60rpx;
			height: 2rpx;
			background: linear-gradient(90deg, #2194FF 0%, #00ff88 100%);
			position: relative;

			&::after {
				content: '';
				position: absolute;
				right: -8rpx;
				top: 50%;
				transform: translateY(-50%);
				width: 6rpx;
				height: 6rpx;
				background: #00ff88;
				border-radius: 50%;
				box-shadow: 0 0 8rpx #00ff88;
			}
		}

		.panel-title {
			font-size: 28rpx;
			color: #ffffff;
			font-weight: 600;
			letter-spacing: 2rpx;
		}

		.header-stats {
			display: flex;
			gap: 20rpx;

			.stat-item {
				text-align: center;

				.stat-value {
					display: block;
					font-size: 32rpx;
					color: #2194FF;
					font-weight: 700;
					line-height: 1;
				}

				.stat-label {
					font-size: 20rpx;
					color: #a0a0a0;
				}
			}
		}
	}

	.panel-grid-lines {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		pointer-events: none;

		.grid-line {
			position: absolute;
			background: rgba(42, 52, 65, 0.3);

			&.horizontal {
				width: 100%;
				height: 1rpx;

				&:nth-child(1) {
					top: 25%;
				}

				&:nth-child(2) {
					top: 50%;
				}

				&:nth-child(3) {
					top: 75%;
				}
			}

			&.vertical {
				height: 100%;
				width: 1rpx;

				&:nth-child(4) {
					left: 20%;
				}

				&:nth-child(5) {
					left: 40%;
				}

				&:nth-child(6) {
					left: 60%;
				}

				&:nth-child(7) {
					left: 80%;
				}
			}
		}
	}
}

/* 工业网格布局 */
.industrial-grid {
	display: flex;
	flex-direction: column;
	gap: 50rpx;
}

.grid-section {
	.section-header {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
		gap: 16rpx;
		position: relative;

		.header-circuit-left {
			width: 40rpx;
			height: 2rpx;
			background: linear-gradient(90deg, #2194FF 0%, transparent 100%);
			position: relative;

			&::before {
				content: '';
				position: absolute;
				left: 0;
				top: 50%;
				transform: translateY(-50%);
				width: 6rpx;
				height: 6rpx;
				background: #2194FF;
				border-radius: 50%;
				box-shadow: 0 0 8rpx #2194FF;
			}

			&.alert {
				background: linear-gradient(90deg, #ff6b35 0%, transparent 100%);

				&::before {
					background: #ff6b35;
					box-shadow: 0 0 8rpx #ff6b35;
				}
			}

			&.analysis {
				background: linear-gradient(90deg, #00ff88 0%, transparent 100%);

				&::before {
					background: #00ff88;
					box-shadow: 0 0 8rpx #00ff88;
				}
			}
		}

		.header-connector {
			width: 16rpx;
			height: 16rpx;
			border: 2rpx solid #2194FF;
			border-radius: 50%;
			position: relative;

			&::after {
				content: '';
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				width: 6rpx;
				height: 6rpx;
				background: #2194FF;
				border-radius: 50%;
			}

			&.alert {
				border-color: #ff6b35;

				&::after {
					background: #ff6b35;
				}
			}

			&.analysis {
				border-color: #00ff88;

				&::after {
					background: #00ff88;
				}
			}
		}

		.section-title {
			font-size: 28rpx;
			color: #ffffff;
			font-weight: 600;
			letter-spacing: 2rpx;
			text-transform: uppercase;
			font-family: 'Arial', sans-serif;
		}

		.header-line-animated {
			flex: 1;
			height: 2rpx;
			background: linear-gradient(90deg, #2194FF 0%, transparent 100%);
			position: relative;

			&::after {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				width: 60rpx;
				height: 100%;
				background: linear-gradient(90deg, rgba(33, 148, 255, 0.8) 0%, transparent 100%);
				animation: lineScan 3s ease-in-out infinite;
			}

			&.alert {
				background: linear-gradient(90deg, #ff6b35 0%, transparent 100%);

				&::after {
					background: linear-gradient(90deg, rgba(255, 107, 53, 0.8) 0%, transparent 100%);
				}
			}

			&.analysis {
				background: linear-gradient(90deg, #00ff88 0%, transparent 100%);

				&::after {
					background: linear-gradient(90deg, rgba(0, 255, 136, 0.8) 0%, transparent 100%);
				}
			}
		}

		.header-terminal {
			width: 12rpx;
			height: 12rpx;
			background: #2194FF;
			border-radius: 50%;
			box-shadow: 0 0 12rpx #2194FF;

			&.alert {
				background: #ff6b35;
				box-shadow: 0 0 12rpx #ff6b35;
			}

			&.analysis {
				background: #00ff88;
				box-shadow: 0 0 12rpx #00ff88;
			}
		}
	}
}

@keyframes lineScan {
	0% {
		transform: translateX(-100%);
	}

	50% {
		transform: translateX(200%);
	}

	100% {
		transform: translateX(-100%);
	}
}

/* 主要模块行布局 */
.module-row {
	display: flex;
	gap: 20rpx;

	.industrial-module {
		flex: 1;
	}
}

/* 分析模块网格布局 */
.module-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
}

/* 增强的工业模块样式 */
.industrial-module {
	background: linear-gradient(135deg, #1a2332 0%, #0f1419 100%);
	border: 1rpx solid #2a3441;
	border-radius: 16rpx;
	position: relative;
	overflow: hidden;
	transition: all 0.3s ease;

	&.enhanced {
		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			height: 2rpx;
			background: linear-gradient(90deg, transparent 0%, #2194FF 50%, transparent 100%);
		}

		&.primary::before {
			background: linear-gradient(90deg, transparent 0%, #2194FF 50%, transparent 100%);
		}

		&.secondary::before {
			background: linear-gradient(90deg, transparent 0%, #00ff88 50%, transparent 100%);
		}

		&.alert::before {
			background: linear-gradient(90deg, transparent 0%, #ff6b35 50%, transparent 100%);
		}

		&.communication::before {
			background: linear-gradient(90deg, transparent 0%, #9c27b0 50%, transparent 100%);
		}
	}

	&:active {
		transform: scale(0.98);
		box-shadow: 0 0 40rpx rgba(33, 148, 255, 0.4);
	}

	.module-frame {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		pointer-events: none;

		.corner-decoration {
			position: absolute;
			width: 20rpx;
			height: 20rpx;
			border: 2rpx solid #2194FF;

			&.tl {
				top: 8rpx;
				left: 8rpx;
				border-right: none;
				border-bottom: none;
			}

			&.tr {
				top: 8rpx;
				right: 8rpx;
				border-left: none;
				border-bottom: none;
			}

			&.bl {
				bottom: 8rpx;
				left: 8rpx;
				border-right: none;
				border-top: none;
			}

			&.br {
				bottom: 8rpx;
				right: 8rpx;
				border-left: none;
				border-top: none;
			}
		}

		&.alert .corner-decoration {
			border-color: #ff6b35;
		}

		&.communication .corner-decoration {
			border-color: #9c27b0;
		}
	}
}

/* 增强的模块头部 */
.module-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 24rpx 16rpx;

	.module-indicator {
		position: relative;
		width: 16rpx;
		height: 16rpx;

		.indicator-center {
			width: 8rpx;
			height: 8rpx;
			border-radius: 50%;
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			z-index: 2;
		}

		.indicator-orbit {
			position: absolute;
			top: 0;
			left: 0;
			width: 16rpx;
			height: 16rpx;
			border: 1rpx solid transparent;
			border-radius: 50%;
			animation: orbit 3s linear infinite;
		}

		&.active {
			.indicator-center {
				background: #00ff88;
				box-shadow: 0 0 12rpx #00ff88;
			}

			.indicator-orbit {
				border-color: #00ff88;
			}
		}

		&.normal {
			.indicator-center {
				background: #666;
			}

			.indicator-orbit {
				border-color: #666;
			}
		}

		&.alert {
			.indicator-center {
				background: #ff6b35;
				box-shadow: 0 0 12rpx #ff6b35;
			}

			.indicator-orbit {
				border-color: #ff6b35;
				animation: alertOrbit 1s ease-in-out infinite;
			}
		}

		&.message {
			.indicator-center {
				background: #9c27b0;
				box-shadow: 0 0 12rpx #9c27b0;
			}

			.indicator-orbit {
				border-color: #9c27b0;
			}
		}
	}

	.module-id {
		font-size: 20rpx;
		color: #666;
		font-family: 'Courier New', monospace;
		letter-spacing: 1rpx;
	}

	.alert-badge,
	.message-badge {
		position: absolute;
		top: 12rpx;
		right: 12rpx;
		background: #ff4444;
		color: white;
		border-radius: 20rpx;
		padding: 4rpx 12rpx;
		font-size: 20rpx;
		min-width: 24rpx;
		text-align: center;
		position: relative;

		.badge-glow {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: #ff4444;
			border-radius: 20rpx;
			animation: badgeGlow 2s ease-in-out infinite;
			z-index: -1;
		}
	}
}

@keyframes orbit {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

@keyframes alertOrbit {

	0%,
	100% {
		transform: rotate(0deg) scale(1);
	}

	50% {
		transform: rotate(180deg) scale(1.2);
	}
}

@keyframes badgeGlow {

	0%,
	100% {
		transform: scale(1);
		opacity: 0.8;
	}

	50% {
		transform: scale(1.2);
		opacity: 0.4;
	}
}

/* 增强的模块内容 */
.module-content {
	display: flex;
	align-items: center;
	padding: 0 24rpx;
	gap: 20rpx;

	.module-icon-container {
		position: relative;

		.icon-backdrop {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			width: 80rpx;
			height: 80rpx;
			border-radius: 50%;
			background: radial-gradient(circle, rgba(33, 148, 255, 0.1) 0%, transparent 70%);

			&.alert {
				background: radial-gradient(circle, rgba(255, 107, 53, 0.1) 0%, transparent 70%);
			}

			&.communication {
				background: radial-gradient(circle, rgba(156, 39, 176, 0.1) 0%, transparent 70%);
			}
		}

		.module-icon {
			width: 60rpx;
			height: 60rpx;
			filter: brightness(1.2);
			position: relative;
			z-index: 2;
		}

		.icon-glow {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			width: 100rpx;
			height: 100rpx;
			border-radius: 50%;
			opacity: 0.3;

			&.primary {
				background: radial-gradient(circle, #2194FF 0%, transparent 70%);
			}

			&.secondary {
				background: radial-gradient(circle, #00ff88 0%, transparent 70%);
			}

			&.alert {
				background: radial-gradient(circle, #ff6b35 0%, transparent 70%);
				animation: alertGlow 2s ease-in-out infinite;
			}

			&.message {
				background: radial-gradient(circle, #9c27b0 0%, transparent 70%);
			}
		}

		.icon-scan-line {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			height: 2rpx;
			background: linear-gradient(90deg, transparent 0%, #2194FF 50%, transparent 100%);
			animation: iconScan 3s ease-in-out infinite;

			&.alert {
				background: linear-gradient(90deg, transparent 0%, #ff6b35 50%, transparent 100%);
			}

			&.message {
				background: linear-gradient(90deg, transparent 0%, #9c27b0 50%, transparent 100%);
			}
		}
	}

	.module-info {
		flex: 1;

		.module-title {
			font-size: 32rpx;
			color: #ffffff;
			font-weight: 600;
			margin-bottom: 8rpx;
			display: block;
		}

		.module-desc {
			font-size: 24rpx;
			color: #a0a0a0;
			display: block;
			margin-bottom: 12rpx;
		}

		.module-metrics {
			margin-top: 12rpx;

			.metric-bar {
				width: 100%;
				height: 4rpx;
				background: rgba(42, 52, 65, 0.5);
				border-radius: 2rpx;
				margin-bottom: 8rpx;
				overflow: hidden;

				.bar-fill {
					height: 100%;
					background: linear-gradient(90deg, #2194FF 0%, #00ff88 100%);
					border-radius: 2rpx;
					transition: width 1s ease;

					&.secondary {
						background: linear-gradient(90deg, #00ff88 0%, #2194FF 100%);
					}
				}
			}

			.metric-text {
				font-size: 20rpx;
				color: #a0a0a0;
			}

			.alert-level-indicator {
				display: flex;
				gap: 4rpx;
				margin-bottom: 8rpx;

				.level-bar {
					flex: 1;
					height: 6rpx;
					background: rgba(42, 52, 65, 0.5);
					border-radius: 3rpx;

					&.active.critical {
						background: #ff4444;
						box-shadow: 0 0 8rpx rgba(255, 68, 68, 0.5);
					}

					&.active.warning {
						background: #ff6b35;
						box-shadow: 0 0 8rpx rgba(255, 107, 53, 0.5);
					}

					&.active.normal {
						background: #00ff88;
						box-shadow: 0 0 8rpx rgba(0, 255, 136, 0.5);
					}
				}
			}

			.signal-bars {
				display: flex;
				gap: 4rpx;
				margin-bottom: 8rpx;

				.signal-bar {
					width: 6rpx;
					background: rgba(42, 52, 65, 0.5);
					border-radius: 3rpx;

					&.bar-1 {
						height: 8rpx;
						background: #00ff88;
					}

					&.bar-2 {
						height: 12rpx;
						background: #2194FF;
					}

					&.bar-3 {
						height: 16rpx;
						background: #2194FF;
					}

					&.bar-4 {
						height: 20rpx;
						background: #00ff88;
					}
				}
			}
		}

		.module-status {
			margin-top: 12rpx;

			.status-indicators {
				display: flex;
				gap: 16rpx;

				.indicator-item {
					display: flex;
					align-items: center;
					gap: 6rpx;

					.status-dot {
						width: 8rpx;
						height: 8rpx;
						border-radius: 50%;

						&.active {
							background: #00ff88;
							box-shadow: 0 0 8rpx rgba(0, 255, 136, 0.5);
							animation: statusBlink 2s ease-in-out infinite;
						}

						&.warning {
							background: #ff6b35;
							box-shadow: 0 0 8rpx rgba(255, 107, 53, 0.5);
							animation: statusBlink 1.5s ease-in-out infinite;
						}
					}

					.status-label {
						font-size: 20rpx;
						color: #a0a0a0;
					}
				}
			}
		}

		.alert-status-display {
			.status-text {
				font-size: 22rpx;
				color: #ffffff;
				margin-bottom: 8rpx;

				&.alert {
					color: #ff6b35;
				}
			}

			.alert-level-indicator {
				display: flex;
				gap: 4rpx;

				.level-bar {
					flex: 1;
					height: 6rpx;
					background: rgba(42, 52, 65, 0.5);
					border-radius: 3rpx;

					&.active.critical {
						background: #ff4444;
						box-shadow: 0 0 8rpx rgba(255, 68, 68, 0.5);
					}

					&.active.warning {
						background: #ff6b35;
						box-shadow: 0 0 8rpx rgba(255, 107, 53, 0.5);
					}

					&.active.normal {
						background: #00ff88;
						box-shadow: 0 0 8rpx rgba(0, 255, 136, 0.5);
					}
				}
			}
		}

		.monitor-display {
			margin-top: 12rpx;

			.data-stream {
				position: relative;
				width: 100%;
				height: 24rpx;
				margin-bottom: 8rpx;

				.stream-line {
					position: absolute;
					top: 50%;
					transform: translateY(-50%);
					width: 100%;
					height: 2rpx;
					background: linear-gradient(90deg, transparent 0%, #00ff88 20%, #2194FF 80%, transparent 100%);
					opacity: 0.6;
				}

				.stream-dots {
					position: absolute;
					top: 50%;
					transform: translateY(-50%);
					width: 100%;
					height: 2rpx;

					.dot {
						position: absolute;
						width: 6rpx;
						height: 6rpx;
						background: #2194FF;
						border-radius: 50%;
						box-shadow: 0 0 8rpx #2194FF;
						animation: dataFlow 3s linear infinite;

						&:nth-child(1) {
							animation-delay: 0s;
						}

						&:nth-child(2) {
							animation-delay: 1s;
						}

						&:nth-child(3) {
							animation-delay: 2s;
						}
					}
				}
			}

			.monitor-status {
				font-size: 20rpx;
				color: #00ff88;
				opacity: 0.8;
			}
		}
	}
}

@keyframes alertGlow {

	0%,
	100% {
		opacity: 0.3;
		transform: translate(-50%, -50%) scale(1);
	}

	50% {
		opacity: 0.6;
		transform: translate(-50%, -50%) scale(1.1);
	}
}

@keyframes iconScan {
	0% {
		transform: translateX(-100%);
		opacity: 0;
	}

	50% {
		opacity: 1;
	}

	100% {
		transform: translateX(100%);
		opacity: 0;
	}
}

@keyframes statusBlink {

	0%,
	100% {
		opacity: 1;
	}

	50% {
		opacity: 0.4;
	}
}

@keyframes statusLinePulse {

	0%,
	100% {
		opacity: 1;
		transform: translateY(-50%) scale(1);
	}

	50% {
		opacity: 0.6;
		transform: translateY(-50%) scale(1.3);
	}
}

@keyframes dataFlow {
	0% {
		transform: translateX(-100%);
	}

	100% {
		transform: translateX(200%);
	}
}



/* 增强的模块底部 */
.module-footer {
	padding: 16rpx 24rpx 20rpx;

	.connection-line {
		width: 100%;
		height: 2rpx;
		background: linear-gradient(90deg, transparent 0%, #333 20%, #333 80%, transparent 100%);
		position: relative;

		.line-pulse {
			position: absolute;
			top: 0;
			left: 0;
			width: 60rpx;
			height: 100%;
			background: linear-gradient(90deg, transparent 0%, rgba(33, 148, 255, 0.8) 50%, transparent 100%);
			animation: linePulse 2s ease-in-out infinite;
		}

		&.primary {
			background: linear-gradient(90deg, transparent 0%, #2194FF 20%, #2194FF 80%, transparent 100%);
		}

		&.secondary {
			background: linear-gradient(90deg, transparent 0%, #00ff88 20%, #00ff88 80%, transparent 100%);
		}

		&.alert {
			background: linear-gradient(90deg, transparent 0%, #ff6b35 20%, #ff6b35 80%, transparent 100%);

			.line-pulse {
				background: linear-gradient(90deg, transparent 0%, rgba(255, 107, 53, 0.8) 50%, transparent 100%);

				&.alert {
					animation: alertPulse 1s ease-in-out infinite;
				}
			}
		}

		&.message {
			background: linear-gradient(90deg, transparent 0%, #9c27b0 20%, #9c27b0 80%, transparent 100%);

			.line-pulse {
				background: linear-gradient(90deg, transparent 0%, rgba(156, 39, 176, 0.8) 50%, transparent 100%);

				&.message {
					animation: messagePulse 1.5s ease-in-out infinite;
				}
			}
		}
	}
}

@keyframes linePulse {
	0% {
		transform: translateX(-100%);
	}

	100% {
		transform: translateX(300%);
	}
}

@keyframes alertPulse {
	0% {
		transform: translateX(-100%);
	}

	100% {
		transform: translateX(300%);
	}
}

@keyframes messagePulse {
	0% {
		transform: translateX(-100%);
	}

	100% {
		transform: translateX(300%);
	}
}

/* 增强的紧凑模块样式 */
.industrial-module.compact.enhanced {
	padding: 24rpx;

	.compact-frame {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		pointer-events: none;

		.corner-mini {
			position: absolute;
			width: 12rpx;
			height: 12rpx;
			border: 1rpx solid #2194FF;

			&.tl {
				top: 4rpx;
				left: 4rpx;
				border-right: none;
				border-bottom: none;
			}

			&.tr {
				top: 4rpx;
				right: 4rpx;
				border-left: none;
				border-bottom: none;
			}

			&.bl {
				bottom: 4rpx;
				left: 4rpx;
				border-right: none;
				border-top: none;
			}

			&.br {
				bottom: 4rpx;
				right: 4rpx;
				border-left: none;
				border-top: none;
			}
		}
	}

	.compact-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		gap: 12rpx;
		margin-bottom: 16rpx;

		.compact-indicator {
			position: relative;
			width: 12rpx;
			height: 12rpx;

			&.normal {
				.mini-pulse {
					width: 6rpx;
					height: 6rpx;
					background: #666;
					border-radius: 50%;
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translate(-50%, -50%);

					&::after {
						content: '';
						position: absolute;
						top: 50%;
						left: 50%;
						transform: translate(-50%, -50%);
						width: 12rpx;
						height: 12rpx;
						border: 1rpx solid #666;
						border-radius: 50%;
						animation: miniPulse 2s ease-in-out infinite;
					}
				}
			}


		}



		.compact-title {
			font-size: 26rpx;
			color: #ffffff;
			font-weight: 500;
			flex: 1;
		}

		.compact-id {
			font-size: 18rpx;
			color: #666;
			font-family: 'Courier New', monospace;
		}
	}

	.compact-content {
		display: flex;
		align-items: center;
		justify-content: space-between;

		.compact-icon-wrapper {
			position: relative;

			.compact-icon {
				width: 48rpx;
				height: 48rpx;
				filter: brightness(1.1);
			}

			.compact-glow {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				width: 60rpx;
				height: 60rpx;
				border-radius: 50%;
				background: radial-gradient(circle, rgba(33, 148, 255, 0.1) 0%, transparent 70%);


			}
		}

		.compact-metrics {
			flex: 1;
			margin-left: 16rpx;

			.mini-chart {
				display: flex;
				align-items: end;
				gap: 4rpx;
				height: 30rpx;

				.chart-bar {
					flex: 1;
					min-height: 4rpx;
					background: linear-gradient(to top, #2194FF 0%, #00ff88 100%);
					border-radius: 2rpx;
					animation: chartPulse 3s ease-in-out infinite;

					&:nth-child(2) {
						animation-delay: 0.5s;
					}

					&:nth-child(3) {
						animation-delay: 1s;
					}
				}
			}





			.water-analysis {
				position: relative;
				width: 100%;
				height: 24rpx;
				margin-bottom: 8rpx;

				.wave-line {
					position: absolute;
					top: 50%;
					transform: translateY(-50%);
					width: 100%;
					height: 2rpx;
					background: linear-gradient(90deg, transparent 0%, #00BCD4 20%, #2196F3 80%, transparent 100%);
					opacity: 0.7;
				}

				.quality-dots {
					position: absolute;
					top: 50%;
					transform: translateY(-50%);
					width: 100%;
					height: 2rpx;

					.dot {
						position: absolute;
						width: 6rpx;
						height: 6rpx;
						background: #00BCD4;
						border-radius: 50%;
						box-shadow: 0 0 8rpx #00BCD4;
						animation: qualityFlow 3s linear infinite;

						&:nth-child(1) {
							animation-delay: 0s;
						}

						&:nth-child(2) {
							animation-delay: 1s;
						}

						&:nth-child(3) {
							animation-delay: 2s;
						}
					}
				}
			}

			.chart-animation {
				position: relative;
				width: 100%;
				height: 24rpx;
				margin-bottom: 8rpx;

				.data-flow-line {
					position: absolute;
					top: 50%;
					transform: translateY(-50%);
					width: 100%;
					height: 2rpx;
					background: linear-gradient(90deg, transparent 0%, #9C27B0 20%, #E91E63 80%, transparent 100%);
					opacity: 0.7;
				}

				.chart-points {
					position: absolute;
					top: 50%;
					transform: translateY(-50%);
					width: 100%;
					height: 2rpx;

					.point {
						position: absolute;
						width: 5rpx;
						height: 5rpx;
						background: #9C27B0;
						border-radius: 50%;
						box-shadow: 0 0 6rpx #9C27B0;
						animation: chartFlow 4s linear infinite;

						&:nth-child(1) {
							animation-delay: 0s;
						}

						&:nth-child(2) {
							animation-delay: 1s;
						}

						&:nth-child(3) {
							animation-delay: 2s;
						}

						&:nth-child(4) {
							animation-delay: 3s;
						}
					}
				}
			}

			.ai-processing {
				position: relative;
				width: 100%;
				height: 24rpx;
				margin-bottom: 8rpx;

				.neural-line {
					position: absolute;
					top: 50%;
					transform: translateY(-50%);
					width: 100%;
					height: 2rpx;
					background: linear-gradient(90deg, transparent 0%, #FF9800 20%, #FFC107 80%, transparent 100%);
					opacity: 0.7;
				}

				.ai-nodes {
					position: absolute;
					top: 50%;
					transform: translateY(-50%);
					width: 100%;
					height: 2rpx;

					.node {
						position: absolute;
						width: 8rpx;
						height: 8rpx;
						background: #FF9800;
						border-radius: 50%;
						box-shadow: 0 0 10rpx #FF9800;
						animation: aiFlow 3.5s linear infinite;

						&:nth-child(1) {
							animation-delay: 0s;
						}

						&:nth-child(2) {
							animation-delay: 1.2s;
						}

						&:nth-child(3) {
							animation-delay: 2.4s;
						}
					}
				}
			}

			.config-sync {
				position: relative;
				width: 100%;
				height: 24rpx;
				margin-bottom: 8rpx;

				.sync-line {
					position: absolute;
					top: 50%;
					transform: translateY(-50%);
					width: 100%;
					height: 2rpx;
					background: linear-gradient(90deg, transparent 0%, #4CAF50 20%, #8BC34A 80%, transparent 100%);
					opacity: 0.7;
				}

				.config-dots {
					position: absolute;
					top: 50%;
					transform: translateY(-50%);
					width: 100%;
					height: 2rpx;

					.dot {
						position: absolute;
						width: 6rpx;
						height: 6rpx;
						background: #4CAF50;
						border-radius: 50%;
						box-shadow: 0 0 8rpx #4CAF50;
						animation: configFlow 3.2s linear infinite;

						&:nth-child(1) {
							animation-delay: 0s;
						}

						&:nth-child(2) {
							animation-delay: 1.1s;
						}

						&:nth-child(3) {
							animation-delay: 2.2s;
						}
					}
				}
			}

			.status-text {
				font-size: 18rpx;
				color: #666;
				opacity: 0.8;
				text-align: center;
			}
		}
	}
}

@keyframes miniPulse {
	0% {
		transform: translate(-50%, -50%) scale(1);
		opacity: 1;
	}

	100% {
		transform: translate(-50%, -50%) scale(2);
		opacity: 0;
	}
}

@keyframes chartPulse {

	0%,
	100% {
		opacity: 0.6;
	}

	50% {
		opacity: 1;
	}
}

@keyframes brainPulse {

	0%,
	100% {
		opacity: 1;
		transform: translate(-50%, -50%) scale(1);
	}

	50% {
		opacity: 0.5;
		transform: translate(-50%, -50%) scale(0.8);
	}
}

@keyframes settingsDot {

	0%,
	100% {
		background: #666;
	}

	50% {
		background: #2194FF;
	}
}

@keyframes qualityFlow {
	0% {
		left: -6rpx;
		top: -3rpx;
		opacity: 0;
	}

	10% {
		opacity: 1;
	}

	90% {
		opacity: 1;
	}

	100% {
		left: 100%;
		top: -3rpx;
		opacity: 0;
	}
}

@keyframes chartFlow {
	0% {
		left: -5rpx;
		top: -2.5rpx;
		opacity: 0;
	}

	10% {
		opacity: 1;
	}

	90% {
		opacity: 1;
	}

	100% {
		left: 100%;
		top: -2.5rpx;
		opacity: 0;
	}
}

@keyframes aiFlow {
	0% {
		left: -8rpx;
		top: -4rpx;
		opacity: 0;
		transform: scale(0.8);
	}

	10% {
		opacity: 1;
		transform: scale(1);
	}

	50% {
		transform: scale(1.2);
	}

	90% {
		opacity: 1;
		transform: scale(1);
	}

	100% {
		left: 100%;
		top: -4rpx;
		opacity: 0;
		transform: scale(0.8);
	}
}

@keyframes configFlow {
	0% {
		left: -6rpx;
		top: -3rpx;
		opacity: 0;
	}

	10% {
		opacity: 1;
	}

	90% {
		opacity: 1;
	}

	100% {
		left: 100%;
		top: -3rpx;
		opacity: 0;
	}
}









/* 保留原有的建议和结论样式 */
.suggestion {
	border-radius: 16rpx;
	margin: 0;
	background-color: rgba(18, 34, 46, 0.5);
}

.conclusion_title {
	padding: 20rpx;
	border-radius: 16rpx;
	background-color: rgba(18, 34, 46, 0.5);
}

.container {
	margin: 0;
	background-color: rgba(33, 148, 255, 0.1);
	border-radius: 16rpx;
	padding: 20rpx;

	.backPressure {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;

		.backPressureImg {
			margin-right: 10rpx;

			.backPressureIocn {
				width: 60rpx;
				height: 60rpx;
			}
		}

		.backPressureText {
			.backPressureTitle {
				font-size: 24rpx;
				font-weight: 400;
				color: rgba(204, 204, 204, 1);
				margin-bottom: 10rpx;
			}

			.backPressureValue {
				font-size: 28rpx;
				font-weight: 500;
				color: rgba(33, 148, 255, 1);
			}
		}
	}
}

.pumpAndfan {
	display: flex;
	justify-content: space-between;
	margin: 0;
	background-color: rgba(33, 148, 255, 0.1);
	border-radius: 16rpx;
	padding: 20rpx;

	.pump,
	.fan {
		display: flex;
		align-items: center;

		.pumpImg {
			margin-right: 10rpx;

			.pumpIocn {
				width: 60rpx;
				height: 60rpx;
			}
		}

		.pumpText {
			.pumpTitle {
				font-size: 24rpx;
				font-weight: 400;
				color: rgba(204, 204, 204, 1);
			}

			.pumpValue {
				font-size: 28rpx;
				font-weight: 500;
				color: rgba(33, 148, 255, 1);
			}
		}
	}
}

.title-text {
	position: relative;
	color: rgba(255, 255, 255, 1);
	font-size: 32rpx;
	line-height: 32rpx;
	padding-left: 25rpx;
}

.title-text::before {
	content: '';
	position: absolute;
	left: 8rpx;
	top: 0;
	width: 8rpx;
	height: 32rpx;
	background-color: #2194FF;
	border-radius: 4rpx;
}

.conclusion {
	border-radius: 16rpx;
	margin: 0;
	background-color: rgba(18, 34, 46, 0.5);
}

::v-deep .uni-collapse {
	background-color: transparent !important;
}

::v-deep .uni-collapse-item__wrap {
	background-color: transparent !important;
}

.custom-title-content {
	background: linear-gradient(135deg, rgba(33, 148, 255, 0.1) 0%, rgba(18, 34, 46, 0.3) 100%);
	border: 1rpx solid rgba(33, 148, 255, 0.2);
	border-radius: 16rpx;
	display: flex;
	flex-direction: column;
	position: relative;
	margin: 0;
	padding: 20rpx;
	backdrop-filter: blur(10rpx);
}

/* 参数可视化器 */
.parameter-visualizer {
	margin-bottom: 24rpx;
}

.range-slider-container {
	margin-bottom: 20rpx;
}

.range-track {
	position: relative;
	height: 12rpx;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 6rpx;
	margin: 30rpx 0;
	box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

/* 安全范围背景 */
.safe-range,
.safe-range-min,
.safe-range-max {
	position: absolute;
	height: 100%;
	border-radius: 6rpx;
	background: linear-gradient(90deg, #00ff88 0%, #2194FF 100%);
	opacity: 0.6;
	box-shadow: 0 0 12rpx rgba(0, 255, 136, 0.3);
}

.safe-range-min {
	background: linear-gradient(90deg, #2194FF 0%, #00ff88 100%);
}

.safe-range-max {
	background: linear-gradient(90deg, #00ff88 0%, #ff6b35 100%);
}

/* 范围标记点 */
.range-markers {
	position: relative;
	width: 100%;
	height: 100%;
}

.marker {
	position: absolute;
	top: -15rpx;
}

.marker-min {
	left: 20%;
	transform: translateX(-50%);
}

.marker-max {
	left: 80%;
	transform: translateX(-50%);
}

.marker-dot {
	width: 16rpx;
	height: 16rpx;
	background: #00ff88;
	border: 2rpx solid #ffffff;
	border-radius: 50%;
	margin: 0 auto 8rpx;
	box-shadow: 0 0 12rpx rgba(0, 255, 136, 0.5);
	animation: markerPulse 2s ease-in-out infinite;
}

.marker-label {
	display: block;
	font-size: 20rpx;
	color: #00ff88;
	text-align: center;
	font-weight: 600;
	text-shadow: 0 0 8rpx rgba(0, 255, 136, 0.5);
}

/* 当前值指示器 */
.current-indicator {
	position: absolute;
	top: -25rpx;
	transform: translateX(-50%);
	z-index: 10;
}

.current-indicator .indicator-pulse {
	width: 24rpx;
	height: 24rpx;
	border-radius: 50%;
	margin: 0 auto 8rpx;
	position: relative;
	animation: indicatorPulse 1.5s ease-in-out infinite;
}

.current-indicator.indicator-safe .indicator-pulse {
	background: #00ff88;
	box-shadow: 0 0 16rpx rgba(0, 255, 136, 0.6);
}

.current-indicator.indicator-warning .indicator-pulse {
	background: #ff6b35;
	box-shadow: 0 0 16rpx rgba(255, 107, 53, 0.6);
}

.current-indicator.indicator-danger .indicator-pulse {
	background: #ff4757;
	box-shadow: 0 0 16rpx rgba(255, 71, 87, 0.6);
}

.indicator-arrow {
	width: 0;
	height: 0;
	border-left: 6rpx solid transparent;
	border-right: 6rpx solid transparent;
	border-bottom: 8rpx solid rgba(255, 255, 255, 0.8);
	margin: 0 auto 4rpx;
}

.indicator-value {
	display: block;
	font-size: 22rpx;
	font-weight: 600;
	text-align: center;
	color: #ffffff;
	text-shadow: 0 0 8rpx rgba(0, 0, 0, 0.5);
}

/* 状态信息面板 */
.status-panel {
	background: rgba(0, 0, 0, 0.2);
	border-radius: 12rpx;
	padding: 16rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.1);
}

.status-row {
	display: flex;
	justify-content: space-between;
	margin-bottom: 12rpx;
}

.status-item {
	flex: 1;
	text-align: center;
}

.status-label {
	display: block;
	font-size: 20rpx;
	color: rgba(204, 204, 204, 0.8);
	margin-bottom: 4rpx;
}

.status-value {
	display: block;
	font-size: 24rpx;
	font-weight: 600;
	color: #ffffff;
}

.status-value.suggested {
	color: #2194FF;
}

.status-value.current.safe {
	color: #00ff88;
}

.status-value.current.warning {
	color: #ff6b35;
}

.status-value.current.danger {
	color: #ff4757;
}

.status-indicator {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	margin-top: 8rpx;
}

.status-indicator.safe {
	background: rgba(0, 255, 136, 0.15);
	border: 1rpx solid rgba(0, 255, 136, 0.3);
}

.status-indicator.warning {
	background: rgba(255, 107, 53, 0.15);
	border: 1rpx solid rgba(255, 107, 53, 0.3);
}

.status-indicator.danger {
	background: rgba(255, 71, 87, 0.15);
	border: 1rpx solid rgba(255, 71, 87, 0.3);
}

.indicator-light {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
	margin-right: 8rpx;
	animation: statusBlink 2s ease-in-out infinite;
}

.status-indicator.safe .indicator-light {
	background: #00ff88;
	box-shadow: 0 0 8rpx rgba(0, 255, 136, 0.5);
}

.status-indicator.warning .indicator-light {
	background: #ff6b35;
	box-shadow: 0 0 8rpx rgba(255, 107, 53, 0.5);
}

.status-indicator.danger .indicator-light {
	background: #ff4757;
	box-shadow: 0 0 8rpx rgba(255, 71, 87, 0.5);
}

.status-text {
	font-size: 22rpx;
	font-weight: 600;
	color: inherit;
}

/* 结论面板 */
.conclusion-panel {
	background: rgba(0, 0, 0, 0.3);
	border-radius: 12rpx;
	padding: 16rpx;
	border: 1rpx solid rgba(33, 148, 255, 0.2);
}

.conclusion-header {
	display: flex;
	align-items: center;
	margin-bottom: 12rpx;
}

.conclusion-title {
	font-size: 24rpx;
	font-weight: 600;
	color: #2194FF;
	margin-right: 12rpx;
}

.conclusion-line {
	flex: 1;
	height: 2rpx;
	background: linear-gradient(90deg, #2194FF 0%, transparent 100%);
}

.conclusion-text {
	font-size: 22rpx;
	line-height: 1.6;
	color: rgba(255, 255, 255, 0.9);
}

.custom-title {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
	min-height: 58rpx;
	padding: 12rpx 16rpx;
	background: linear-gradient(90deg, rgba(33, 148, 255, 0.05) 0%, rgba(0, 0, 0, 0.1) 100%);
	border-radius: 12rpx;
	border: 1rpx solid rgba(33, 148, 255, 0.15);
}

.custom-left {
	flex: 1;
	text-align: left;
	font-size: 26rpx;
	font-weight: 600;
	color: #2194FF;
	letter-spacing: 2rpx;
}

.custom-right {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	gap: 12rpx;
}

.custom-right-value {
	font-size: 32rpx;
	font-weight: 700;
	color: #ffffff;
	text-shadow: 0 0 8rpx rgba(255, 255, 255, 0.3);
}

.custom-right-state {
	padding: 6rpx 12rpx;
	border-radius: 16rpx;
	font-size: 22rpx;
	font-weight: 600;
	backdrop-filter: blur(5rpx);
}

.custom-right-state.state-normal {
	background: rgba(0, 255, 136, 0.2);
	color: #00ff88;
	border: 1rpx solid rgba(0, 255, 136, 0.3);
	box-shadow: 0 0 12rpx rgba(0, 255, 136, 0.2);
}

.custom-right-state.state-low {
	background: rgba(255, 107, 53, 0.2);
	color: #ff6b35;
	border: 1rpx solid rgba(255, 107, 53, 0.3);
	box-shadow: 0 0 12rpx rgba(255, 107, 53, 0.2);
}

.custom-right-state.state-high {
	background: rgba(255, 71, 87, 0.2);
	color: #ff4757;
	border: 1rpx solid rgba(255, 71, 87, 0.3);
	box-shadow: 0 0 12rpx rgba(255, 71, 87, 0.2);
}

.custom-right-state span {
	color: inherit;
}

/* 动画关键帧 */
@keyframes markerPulse {

	0%,
	100% {
		opacity: 0.8;
		transform: scale(1);
	}

	50% {
		opacity: 1;
		transform: scale(1.2);
	}
}

@keyframes indicatorPulse {

	0%,
	100% {
		opacity: 0.9;
		transform: scale(1);
	}

	50% {
		opacity: 1;
		transform: scale(1.3);
	}
}

@keyframes statusBlink {

	0%,
	100% {
		opacity: 1;
	}

	50% {
		opacity: 0.5;
	}
}
</style>