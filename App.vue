<script>
  import config from './config'
  import store from '@/store'

  import { getToken } from '@/utils/auth'

  export default {
    onLaunch: function() {
      this.initApp()
    },
    methods: {
      // 初始化应用
      initApp() {
        // 初始化应用配置
        this.initConfig()
        // 检查用户登录状态
        //#ifdef H5
        this.checkLogin()
        //#endif
      },
      initConfig() {
        this.globalData.config = config
      },
      checkLogin() {
        if (!getToken()) {
          this.$tab.reLaunch('/pages/login')
        }
      }
    }
  }
</script>

<style lang="scss">
  @import '@/static/scss/index.scss';
  page{
			font-family: MiSans;
			// 全局背景图已注释 - 为App端优化性能
			// background-image: url('http://*************:9000/aliyun/2025/04/15/20138b54a8454ec3a0cef720efaf19ea.png');
			// background-image: url('http://**************:9000/iot/2024/11/05/07b18c5b5e0745588e0b99cbe6fd91a0.png');//本地测试服务器
			// background-size: cover;
			// background-repeat: no-repeat;
			// background-position: center;
			// background-attachment: fixed;
			background-color: #131b2e; // 使用纯色背景替代
  }
  .nav-list {
  	display: flex;
  	flex-wrap: wrap;
  	justify-content: space-between;
  }

  .nav-li {
  	padding: 20rpx 20rpx 20rpx 20rpx;
  	border-radius: 12upx;
  	width: 45%;
  	margin: 0 2.5% 20upx;
  	background-size: cover;
  	background-position: center;
  	position: relative;
  	z-index: 1;
  }

  .nav-li::after {
  	content: "";
  	position: absolute;
  	z-index: -1;
  	background-color: inherit;
  	width: 100%;
  	height: 100%;
  	left: 0;
  	bottom: -10%;
  	border-radius: 10upx;
  	opacity: 0.2;
  	transform: scale(0.9, 0.9);
  }

  .nav-li.cur {
  	color: #fff;
  	background: rgb(94, 185, 94);
  	box-shadow: 4upx 4upx 6upx rgba(94, 185, 94, 0.4);
  }

  .nav-title {
  	font-size: 32upx;
  	font-weight: 700;
	margin-top: 25upx;
  }

  // .nav-title::first-letter {
  // 	font-size: 40upx;
  // 	margin-right: 4upx;
  // }

  .nav-name {
  	font-size: 25upx;
  	text-transform: Capitalize;
  	position: relative;
  }

  .nav-name::before {
  	content: "";
  	position: absolute;
  	display: block;
  	width: 40upx;
  	height: 6upx;
  	background: #fff;
  	bottom: 0;
  	right: 0;
  	opacity: 0.5;
  }

  .nav-name::after {
  	content: "";
  	position: absolute;
  	display: block;
  	width: 100upx;
  	height: 1px;
  	background: #fff;
  	bottom: 0;
  	right: 40upx;
  	opacity: 0.3;
  }

  // .nav-name::first-letter {
  // 	font-weight: bold;
  // 	font-size: 36upx;
  // 	margin-right: 1px;
  // }

  .nav-li text {
  	position: absolute;
  	right: 20upx;
  	top: 10upx;
  	font-size: 52upx;
  	width: 60upx;
  	height: 60upx;
  	text-align: center;
  	line-height: 60upx;
  }

  .text-light {
  	font-weight: 300;
  }

  @keyframes show {
  	0% {
  		transform: translateY(-50px);
  	}

  	60% {
  		transform: translateY(40upx);
  	}

  	100% {
  		transform: translateY(0px);
  	}
  }

  @-webkit-keyframes show {
  	0% {
  		transform: translateY(-50px);
  	}

  	60% {
  		transform: translateY(40upx);
  	}

  	100% {
  		transform: translateY(0px);
  	}
  }
</style>
