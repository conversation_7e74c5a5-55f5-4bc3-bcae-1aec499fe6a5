import config from '@/config'
import storage from '@/utils/storage'
import constant from '@/utils/constant'
import { toast } from '@/utils/common'
import { Encrypt } from '@/utils/encryptionService'
import {
	login,
	logout,
	getInfo,
	entLogin,
	entXcxMoile,
	accountLogin
} from '@/api/login'
import {listByIds } from "@/api/system/user";
import {
	getToken,
	setToken,
	removeToken,
	setClientId,
	setRole
} from '@/utils/auth'
import {
	getCurrentHome
} from '@/api/system/space'

const baseUrl = ''

const user = {
	state: {
		token: getToken(),
		name: storage.get(constant.name),
		avatar: storage.get(constant.avatar),
		roles: storage.get(constant.roles),
		permissions: storage.get(constant.permissions),
		home: storage.get(constant.home)
	},

	mutations: {
		SET_TOKEN: (state, token) => {
			state.token = token
		},
		SET_NAME: (state, name) => {
			state.name = name
			storage.set(constant.name, name)
		},
		SET_AVATAR: (state, avatar) => {
			state.avatar = avatar
			storage.set(constant.avatar, avatar)
		},
		SET_ROLES: (state, roles) => {
			state.roles = roles
			storage.set(constant.roles, roles)
		},
		SET_PERMISSIONS: (state, permissions) => {
			state.permissions = permissions
			storage.set(constant.permissions, permissions)
		},
		SET_HOME: (state, home) => {
			state.home = home
			storage.set(constant.home, home)
		}
	},

	actions: {
		// 账号密码登录
		AccountLogin({ commit }, userInfo) {
			const username = userInfo.username.trim()
			const password = userInfo.password
			// 使用crypto-js加密密码
			const encryptedPassword = Encrypt(password)
			
			return new Promise((resolve, reject) => {
				accountLogin(username, encryptedPassword).then(res => {
					// 保存用户名和密码（加密）
					uni.setStorageSync('savedUsername', username);
					uni.setStorageSync('savedPassword', Encrypt(password));
					
					const code = res.code || 200
					if (code != 200) {
						toast(res.msg)
						reject(res.msg)
						return
					}
					setToken(res.data.token)
					commit('SET_TOKEN', res.data.token)
					setClientId(new Date().getTime().toString())
					resolve(res)
				}).catch(error => {
					reject(error)
				})
			})
		},

		// 微信登录（保留作为备用）
		Login({ commit }, userInfo) {
			const username = userInfo.username.trim()
			const password = Encrypt(userInfo.password)
			const code = userInfo.code
			const uuid = userInfo.uuid
			return new Promise((resolve, reject) => {
				login(username, password, code).then(res => {
					uni.setStorageSync('savedUsername', username);
					uni.setStorageSync('savedPassword', password);
					const code = res.code || 200
					if (code != 200) {
						toast(res.msg)
						return
					}
					setToken(res.data.token)
					commit('SET_TOKEN', res.data.token)
					setClientId(new Date().getTime().toString())
					resolve()
				}).catch(error => {
					reject(error)
				})
			})
		},

		// 获取用户信息
		async GetInfo({ commit, state }) {
			try {
				const res = await getInfo();
				const user = res.data.user;

				// 打印获取到的权限数据
				console.log('res', res.data.permissions);

				// 确保权限数据存在并正确设置

				// 准备调用 listByIds 接口以获取头像的完整 URL
				const params = {
					data: [user.avatar]
				}
				const listRes = await listByIds(params)

				// 获取头像的完整 URL
				const avatarUrl = listRes.data[0].url
				const avatar = avatarUrl

				// 获取用户名，处理可能的空值情况
				const username = (user == null || user.nickName === "" || user.nickName == null) ? "" : user.nickName

				// 打印其他重要信息
				console.log('User name:', username);
				console.log('Avatar URL:', avatarUrl);

				// 设置用户角色
				setRole(user.roles)

				// 提交用户名和头像到 Vuex 状态
				commit('SET_NAME', username)
				commit('SET_AVATAR', avatar)
				commit('SET_PERMISSIONS', res.data.permissions);

				// 获取当前主页信息
				const homeRes = await getCurrentHome()
				commit('SET_HOME', homeRes.data)

				return res
			} catch (error) {
				console.error('Failed to fetch user info:', error);
				throw error
			}
		},

		// 退出系统
		LogOut({ commit, state }) {
			return new Promise((resolve, reject) => {
				commit('SET_TOKEN', '')
				commit('SET_ROLES', [])
				commit('SET_PERMISSIONS', [])
				removeToken()
				storage.clean()
				resolve()
			})
		}
	}
}

export default user
