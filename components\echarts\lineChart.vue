<template>
	<view style="width: 100% ;height: 100%;margin:20rpx 0;">
		<qiun-data-charts type="area" :opts="calleqopts" :ontouch='true' :loadingType="0" :chartData="calleqdata" background="rgba(9, 20, 28,1)" canvas2d/>
	</view>
</template>
 
<script>
	export default {
		name: "failure-rate",
		// 接受父组件传递数据
		props: ['data'],
		watch: {
			data: {
				deep: true,
				handler(newVal) {
					// 先判断newVal里面是否存在xdata

					console.log(newVal,'---newVal');
					if (newVal.xdata) {

						this.calleqdata.categories = newVal.xdata;
					this.calleqdata.series = newVal.series.map(serie => ({
						name: serie.name,
						data: serie.data
					}));
					this.calleqopts.yAxis.data[0].title = newVal.title;
					// itemCount数量控制 当xNumber为1时设置为8为0时设置为5
					this.calleqopts.xAxis.itemCount = newVal.xNumber === 1 ? 8 : 5;
					}else{
						this.calleqdata.categories = [];
						this.calleqdata.series = [];
						// this.calleqopts.yAxis.data[0].title = '';
						// this.calleqopts.xAxis.itemCount = 5;
					}
					// console.log(newVal,'---newVal');
					
				}
			}
		},
		data() {
			return {
				// 报警故障配置
				calleqopts: {
					// 
					fontColor:"#FFFFFF",
					fontSize:10,
					// 数据颜色
					color: ["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],
					touchMoveLimit:25,
					// 内边距-矫正父组件布局
					padding: [20, 5, 5, 5],
					// 是否显示折线图每个节点数据
					dataLabel: true,
					// 是否在每个节点-显示节点
					dataPointShape: false,
					// 图例配置
					legend: {
						// 把图例放在图表上方
						position: "bottom",
						float:"bottom",
						
					},
					enableScroll: true,
					// x轴配置
					xAxis: {
						// 不绘制纵向网格
						disableGrid: true,
						itemCount: 8,
						scrollShow:true,//开启滚动条
						scrollBackgroundColor:'#082b42',
						scrollColor:'#064060',
						axisLineColor:'#0a214d',
						fontColor:'#999999',
						fontSize:10,
						scrollAlign:'right',
					},
					// y轴配置
					yAxis: {
						// 虚线-实线
						gridType: "dash", 
						// 虚线单位
						dashLength: 2,
						// 开启y轴单位
						fontColor:'#999999',
						fontSize:10,
						showTitle: true,
						gridColor:"#064060",
						// y轴配置
						data: [{
							// y轴单位
							title: '',
							// y轴单位颜色
							titleFontColor: "#999999",
							// 字体大小
							titleFontSize:12,
							// 纵向偏移
							titleOffsetY:-5,
							// 横向偏移:
							titleOffsetX:350
						}]
					},
					extra: {
						area: {
							// 曲线圆滑模式
							type: "curve",
							opacity:0.2,
							// 折线的宽度
							width: 2,
							// 点击弹出框时，节点样式
							activeType: "hollow",
							// 自定义渐变色
							linearType: "custom",
							// 开启阴影
							onShadow: true,
							// 水平动画
							animation: "horizontal"
						}
					}
				},
				// 报警故障数据
				calleqdata: {
					// x轴数据
					categories: [],
					// 表格数据 - 一个对象就是一个数据
					series: []
				},
			};
		},
	}
</script>