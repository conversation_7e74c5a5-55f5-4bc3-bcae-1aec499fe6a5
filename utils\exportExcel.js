// // utils.js
// import * as XLSX from 'xlsx';

// export function generateExcel(data) {
//   const ws = XLSX.utils.json_to_sheet(data);
//   const wb = XLSX.utils.book_new();
//   XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
//   const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
//   return wbout;
// }

// export function uploadAndDownloadExcel(data, successCallback, errorCallback) {
//   const wbout = generateExcel(data);
//   const filePath = `${uni.env.USER_DATA_PATH}/temp.xlsx`;
//   const fs = uni.getFileSystemManager();
  
//   fs.writeFile({
//     filePath: filePath,
//     data: wbout,
//     encoding: 'binary',
//     success: () => {
//       uni.uploadFile({
//         url: 'https://your-server.com/upload',
//         filePath: filePath,
//         name: 'file',
//         header: {
//           'Content-Type': 'application/octet-stream'
//         },
//         success: (res) => {
//           const downloadUrl = JSON.parse(res.data).downloadUrl;
//           if (successCallback) {
//             successCallback(downloadUrl);
//           }
//           uni.downloadFile({
//             url: downloadUrl,
//             success: (res) => {
//               if (res.statusCode === 200) {
//                 uni.saveFile({
//                   tempFilePath: res.tempFilePath,
//                   success: (saveRes) => {
//                     uni.showToast({
//                       title: '文件已保存',
//                       icon: 'success'
//                     });
//                   }
//                 });
//               }
//             }
//           });
//         },
//         fail: (err) => {
//           if (errorCallback) {
//             errorCallback(err);
//           }
//         }
//       });
//     },
//     fail: (err) => {
//       if (errorCallback) {
//         errorCallback(err);
//       }
//     }
//   });
// }
