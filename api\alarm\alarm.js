import request from '@/utils/request'

// 查询字典/system/dict/data/type
export function alarmDict(data) {
  return request({
    url: '/system/dict/data/type',
    method: 'Post',
	data: data
  })
}

// 查询告警列表
export function alarmList(data) {
  return request({
    url: '/alert/list',
    method: 'Post',
	data: data
  })
}

// 查询告警记录
export function alarmRecord(data) {
  return request({
    url: '/alert/selectAlertRecordPage',
    method: 'Post',
	data: data
  })
}

// 点击已读
export function alarmRead(data) {
  return request({
    url: '/alert/read',
    method: 'Post',
	data: data
  })
}

// 告警级别以及未读数量
export function alarmLevel(data) {
  return request({
    url: '/alert/level/stats',
    method: 'Post',
	data: data
  })
}

// 消息中心未读数量
export function unreadMsg(data) {
  return request({
    url: '/notify/message/unread/cnt',
    method: 'Post',
	data: data
  })
}

