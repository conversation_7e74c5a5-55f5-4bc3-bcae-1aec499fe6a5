const options = {
	year: "numeric",
	month: "2-digit",
	day: "2-digit",
	hour: "2-digit",
	minute: "2-digit",
	second: "2-digit",
};

export function getCurrentTime2() {
	let a = new Date()
	const year = a.getFullYear();
	const month = String(a.getMonth() + 1).padStart(2, '0');
	const day = String(a.getDate()).padStart(2, '0');
	const formattedDate= `${year}-${month}-${day}`
	console.log(formattedDate,';formattedDate--');
	return formattedDate
}
// 时间戳转时间
export function changeTimesStamp(timestamp) {
	const date = new Date(timestamp);
	const year = date.getFullYear();
	const month = ("0" + (date.getMonth() + 1)).slice(-2);
	const day = ("0" + date.getDate()).slice(-2);
	const hours = ("0" + date.getHours()).slice(-2);
	const minutes = ("0" + date.getMinutes()).slice(-2);
	const seconds = ("0" + date.getSeconds()).slice(-2);

	const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
	// return Intl.DateTimeFormat("zh-CN", options).format(timestamp)

	return formattedDate
}

export function changeTimesStamp1(timestamp) {
	const date = new Date(timestamp);
	const year = date.getFullYear();
	const month = ("0" + (date.getMonth() + 1)).slice(-2);
	const day = ("0" + date.getDate()).slice(-2);
	const hours = ("0" + date.getHours()).slice(-2);
	const minutes = ("0" + date.getMinutes()).slice(-2);
	const seconds = ("0" + date.getSeconds()).slice(-2);

	const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}`
	// return Intl.DateTimeFormat("zh-CN", options).format(timestamp)
	return formattedDate
}

export const generateUUID = () => {
	if (typeof crypto === 'object') {
		if (typeof crypto.randomUUID === 'function') {
			return crypto.randomUUID()
		}
		if (typeof crypto.getRandomValues === 'function' && typeof Uint8Array === 'function') {
			const callback = (c) => {
				const num = Number(c)
				return (num ^ (crypto.getRandomValues(new Uint8Array(1))[0] & (15 >> (num / 4)))).toString(16)
			}
			return '10000000-1000-4000-8000-100000000000'.replace(/[018]/g, callback)
		}
	}
	let timestamp = new Date().getTime()
	let performanceNow = (typeof performance !== 'undefined' && performance.now && performance.now() * 1000) || 0
	return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
		let random = Math.random() * 16
		if (timestamp > 0) {
			random = (timestamp + random) % 16 | 0
			timestamp = Math.floor(timestamp / 16)
		} else {
			random = (performanceNow + random) % 16 | 0
			performanceNow = Math.floor(performanceNow / 16)
		}
		return (c === 'x' ? random : (random & 0x3) | 0x8).toString(16)
	})
}

// hh:mm:ss
export function CurrentTime() {
    const currentDateTime = new Date();
    const currentHours = String(currentDateTime.getHours()).padStart(2, '0');
    const currentMinutes = String(currentDateTime.getMinutes()).padStart(2, '0');
    const currentSeconds = String(currentDateTime.getSeconds()).padStart(2, '0');
    
    return {
        currentHours,
        currentMinutes,
        currentSeconds
    };
}
// 时间+00:00:00  和当前时间hh:mm:ss
export function generateTimeRange(data) {
    const { currentHours, currentMinutes, currentSeconds } = CurrentTime();

    let startTime, endTime;

    if (typeof data === 'string') {
        // 第一种数据结构
        startTime = `${data} 00:00:00`;
        endTime = `${data} ${currentHours}:${currentMinutes}:${currentSeconds}`;
    } else if (Array.isArray(data)) {
        // 第二种数据结构
        startTime = `${data[0]} 00:00:00`;
        endTime = `${data[data.length - 1]} 23:59:59`;
    } else {
        throw new Error('Invalid data format');
    }

    return { startTime, endTime };
}
// HH:mm
export function getCurrentTime() {
  const now = new Date();
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  return `${hours}:${minutes}`;
}
// 今天
export function getToday() {
	const today = new Date();
	const dateStr = today.toISOString().split('T')[0];
	const currentHours = String(today.getHours()).padStart(2, '0');
	const currentMinutes = String(today.getMinutes()).padStart(2, '0');
	const currentSeconds = String(today.getSeconds()).padStart(2, '0');

	const startTime = `${dateStr} 00:00:00`;
	const endTime = `${dateStr} ${currentHours}:${currentMinutes}:${currentSeconds}`;
	console.log(startTime,endTime);
	return { startTime, endTime };
}
// 昨天
export function getYesterday() {
	const yesterday = new Date();
	yesterday.setDate(yesterday.getDate() - 1);
	const dateStr = yesterday.toISOString().split('T')[0];

	const startTime = `${dateStr} 00:00:00`;
	const endTime = `${dateStr} 23:59:59`; // 昨天的结束时间定义为当天最后一秒

	return { startTime, endTime };
}
// 本周 - 从本周开始的 00:00:00 到当前时间
export function getThisWeek() {
    const today = new Date();
    const dayOfWeek = today.getDay() || 7; // 如果是星期天，将其视为第7天
    const startOfWeek = new Date(today);

    // 设置开始时间为本周一的 00:00:00
    startOfWeek.setDate(today.getDate() - dayOfWeek + 1);
    startOfWeek.setHours(0, 0, 0, 0);

    const startDateStr = startOfWeek.toISOString().split('T')[0];

    // 获取当前时间的小时、分钟和秒
    const currentHours = String(today.getHours()).padStart(2, '0');
    const currentMinutes = String(today.getMinutes()).padStart(2, '0');
    const currentSeconds = String(today.getSeconds()).padStart(2, '0');
    const endDateStr = today.toISOString().split('T')[0];

    const startTime = `${startDateStr} 00:00:00`;
    const endTime = `${endDateStr} ${currentHours}:${currentMinutes}:${currentSeconds}`;

    return { startTime, endTime };
}

// 上周
export function getLastWeek() {
    const today = new Date();
    const dayOfWeek = today.getDay() || 7; // 如果是星期天，将其视为第7天
    const startOfLastWeek = new Date(today);
    const endOfLastWeek = new Date(today);

    // 设置上周一的 00:00:00
    startOfLastWeek.setDate(today.getDate() - dayOfWeek - 6);
    startOfLastWeek.setHours(0, 0, 0, 0);

    // 设置上周日的 23:59:59
    endOfLastWeek.setDate(today.getDate() - dayOfWeek);
    endOfLastWeek.setHours(23, 59, 59, 999);

    const startTime = startOfLastWeek.toISOString().replace("T", " ").split(".")[0];
    const endTime = endOfLastWeek.toISOString().replace("T", " ").split(".")[0];

    return { startTime, endTime };
}


// 本月
// 本月 - 从本月开始的 00:00:00 到当前时间
export function getThisMonth() {
    const today = new Date();
    const year = today.getFullYear();
    const month = today.getMonth();

    // 设置本月第一天的开始时间为 00:00:00
    const startDate = new Date(year, month, 1);
    const startDateStr = startDate.toISOString().split('T')[0];

    // 获取当前时间的小时、分钟和秒
    const currentHours = String(today.getHours()).padStart(2, '0');
    const currentMinutes = String(today.getMinutes()).padStart(2, '0');
    const currentSeconds = String(today.getSeconds()).padStart(2, '0');
    const endDateStr = today.toISOString().split('T')[0];

    const startTime = `${startDateStr} 00:00:00`;
    const endTime = `${endDateStr} ${currentHours}:${currentMinutes}:${currentSeconds}`;

    return { startTime, endTime };
}


// 上月
export function getLastMonth() {
    const today = new Date();
    const year = today.getFullYear();
    const lastMonth = today.getMonth() - 1;

    // 设置上月第一天的 00:00:00
    const startDate = new Date(year, lastMonth, 1);
    startDate.setHours(0, 0, 0, 0);

    // 设置上月最后一天的 23:59:59
    const endDate = new Date(year, lastMonth + 1, 0);
    endDate.setHours(23, 59, 59, 999);

    const startTime = startDate.toISOString().replace("T", " ").split(".")[0];
    const endTime = endDate.toISOString().replace("T", " ").split(".")[0];

    return { startTime, endTime };
}

// 今年
// 今年 - 从今年开始的 00:00:00 到当前时间
export function getThisYear() {
    const today = new Date();
    const year = today.getFullYear();

    // 设置今年第一天的开始时间为 00:00:00
    const startDate = new Date(year, 0, 1);
    const startDateStr = startDate.toISOString().split('T')[0];

    // 获取当前时间的小时、分钟和秒
    const currentHours = String(today.getHours()).padStart(2, '0');
    const currentMinutes = String(today.getMinutes()).padStart(2, '0');
    const currentSeconds = String(today.getSeconds()).padStart(2, '0');
    const endDateStr = today.toISOString().split('T')[0];

    const startTime = `${startDateStr} 00:00:00`;
    const endTime = `${endDateStr} ${currentHours}:${currentMinutes}:${currentSeconds}`;

    return { startTime, endTime };
}
