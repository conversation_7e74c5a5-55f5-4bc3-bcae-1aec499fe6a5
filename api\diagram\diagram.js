import request from '@/utils/request'

// 查询图表标签
export function diagramConfiglable(data) {
  return request({
    url: '/project/powerPointConfig/all',
    method: 'Post',
	data: data
  })
}

// 点击点位获取图表数据
export function diagramConfigEcharts(data) {
  return request({
    url: '/device/deviceProperty/downSampling/list',
    method: 'Post',
	data: data
  })
}

// 点击点位获取图表数据
export function diagramConfigEchartsWechat(data) {
  return request({
    url: '/device/deviceProperty/downSampling/list/wechat',
    method: 'Post',
	data: data
  })
}