<template>
	<view class="project-page">
		<!-- 使用AppHeader组件 -->
		<AppHeader flex iconShow title="[CIMS] 项目管理" />

		<view class="container">
			<!-- 说明文字 -->
			<view class="instruction">
				<text class="instruction-text">选择项目进行切换</text>
			</view>

			<!-- 空数据组件 -->
			<EmptyData
				:show="projectData.length === 0"
				title="暂无项目"
				description="当前系统中没有可用的项目"
			/>

			<!-- 项目列表 -->
			<view class="project-list" v-if="projectData.length > 0">
				<view
					class="project-item"
					:class="{ 'selected': selectedIndex === item.id }"
					v-for="item in projectData"
					:key="item.id"
					@click="selectItem(item)"
				>
					<view class="project-name">{{ item.name }}</view>

					<view class="status-icon" :class="{ 'selected': selectedIndex === item.id }">
						<text v-if="selectedIndex === item.id">✓</text>
						<text v-else>○</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {getProject ,getProjectList,setProject} from "@/utils/auth"
	import EmptyData from '@/components/EmptyData/EmptyData.vue';

	export default {
		components: {
			EmptyData
		},
		data() {
			return {
				projectData: [], // 项目数据列表
				selectedIndex: null, // 当前选中的项目ID
				seletData: {}, // 当前项目数据
			}
		},
		methods: {
			// 选择项目
			selectItem(item) {
				// 如果点击的是当前项目，不做任何操作
				if (this.selectedIndex === item.id) {
					return;
				}

				// 显示确认对话框
				uni.showModal({
					title: '确认切换',
					content: `确定要切换到项目"${item.name}"吗？`,
					confirmText: '确认',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							this.performSwitch(item);
						}
					}
				});
			},

			// 执行项目切换
			performSwitch(project) {
				uni.showLoading({
					title: '切换中...'
				});

				setTimeout(() => {
					try {
						setProject(project);
						this.selectedIndex = project.id;
						this.seletData = project;

						uni.hideLoading();
						uni.showToast({
							title: '切换成功',
							icon: 'success',
							duration: 1500
						});

						// 延迟返回上一页
						setTimeout(() => {
							uni.navigateBack();
						}, 1000);

					} catch (error) {
						uni.hideLoading();
						uni.showToast({
							title: '切换失败',
							icon: 'error',
							duration: 2000
						});
					}
				}, 800);
			},

			// 获取数据
			getdata(){
				this.projectData = getProjectList() || [];
				this.seletData = getProject() || {};
				this.selectedIndex = this.seletData.id;
			}
		},
		mounted() {
			this.getdata();
		}
	}
</script>

<style scoped>
/* 主容器 */
.project-page {
	min-height: 100vh;
	background: #131b2e;
}

/* 容器 */
.container {
	padding: 30rpx 20rpx;
}

/* 说明文字 */
.instruction {
	margin-bottom: 30rpx;
	text-align: center;
}

.instruction-text {
	font-size: 26rpx;
	color: #cccccc;
}

/* 项目列表 */
.project-list {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

/* 项目卡片 */
.project-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 25rpx;
	background: #293e54;
	border: 2rpx solid transparent;
	border-radius: 12rpx;
	transition: all 0.3s ease;
}

.project-item:active {
	transform: scale(0.98);
}

.project-item.selected {
	border-color: #00ff88;
	background: #0c4074;
}

/* 项目名称 */
.project-name {
	font-size: 28rpx;
	font-weight: 500;
	color: #ffffff;
	flex: 1;
}

.project-item.selected .project-name {
	color: #00ff88;
}

/* 状态图标 */
.status-icon {
	width: 35rpx;
	height: 35rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 20rpx;
	font-weight: bold;
	background: #293e54;
	border: 2rpx solid #cccccc;
	color: #cccccc;
	transition: all 0.3s ease;
}

.status-icon.selected {
	background: #00ff88;
	border-color: #00ff88;
	color: #ffffff;
}
</style>
